import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import precision_recall_fscore_support, confusion_matrix, classification_report

import copy
import os
import gc
import random
from tqdm import tqdm

import torch
from torch.utils.data import DataLoader, Dataset
from torchvision import datasets, transforms

from src.data import *
from src.data.data import noniid_dirichlet
# Import models conditionally based on pruning mode - will be done after args parsing
from src.pruning import *
from src.sub_fedavg import *


def calculate_detailed_metrics(client, dataset_test, device, num_classes):
    """
    计算客户端的详细指标：precision, recall, F1, False alarm rate, confusion matrix

    Args:
        client: 客户端对象
        dataset_test: 测试数据集
        device: 计算设备
        num_classes: 类别数量

    Returns:
        dict: 包含所有指标的字典
    """
    # 客户端模型存储在 net 属性中
    client.net.eval()

    all_predictions = []
    all_targets = []

    # 获取所有预测和真实标签
    with torch.no_grad():
        for data, target in dataset_test:
            data, target = data.to(device), target.to(device)
            output = client.net(data)
            pred = output.argmax(dim=1)

            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())

    # 转换为numpy数组
    y_true = np.array(all_targets)
    y_pred = np.array(all_predictions)

    # 计算precision, recall, F1
    precision, recall, f1, support = precision_recall_fscore_support(
        y_true, y_pred, average=None, zero_division=0
    )

    # 计算宏平均和微平均
    precision_macro, recall_macro, f1_macro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='macro', zero_division=0
    )
    precision_micro, recall_micro, f1_micro, _ = precision_recall_fscore_support(
        y_true, y_pred, average='micro', zero_division=0
    )

    # 计算混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=list(range(num_classes)))

    # 计算False Alarm Rate (FPR) for each class
    false_alarm_rates = []
    for i in range(num_classes):
        # True Negatives: correctly predicted as not class i
        tn = np.sum(cm) - (np.sum(cm[i, :]) + np.sum(cm[:, i]) - cm[i, i])
        # False Positives: incorrectly predicted as class i
        fp = np.sum(cm[:, i]) - cm[i, i]

        # False Alarm Rate = FP / (FP + TN)
        if (fp + tn) > 0:
            far = fp / (fp + tn)
        else:
            far = 0.0
        false_alarm_rates.append(far)

    # 计算整体False Alarm Rate (平均)
    avg_false_alarm_rate = np.mean(false_alarm_rates)

    # 计算准确率
    accuracy = np.sum(y_pred == y_true) / len(y_true)

    return {
        'accuracy': accuracy,
        'precision_per_class': precision,
        'recall_per_class': recall,
        'f1_per_class': f1,
        'support_per_class': support,
        'precision_macro': precision_macro,
        'recall_macro': recall_macro,
        'f1_macro': f1_macro,
        'precision_micro': precision_micro,
        'recall_micro': recall_micro,
        'f1_micro': f1_micro,
        'false_alarm_rates': false_alarm_rates,
        'avg_false_alarm_rate': avg_false_alarm_rate,
        'confusion_matrix': cm,
        'y_true': y_true,
        'y_pred': y_pred
    }
from src.client import *
from src.utils.options_u import args_parser

class CSVDataset(Dataset):
    """Custom Dataset class for CSV data with sliding window and normalization."""

    def __init__(self, csv_path: str, window_size: int = 1, train: bool = True,
                 train_ratio: float = 0.7, scaler=None, random_seed: int = 42,
                 train_indices=None, val_indices=None):
        """
        Initialize CSV dataset.

        Args:
            csv_path: Path to CSV file
            window_size: Size of sliding window for sequence data
            train: Whether this is training data (True) or validation data (False)
            train_ratio: Ratio of data to use for training
            scaler: Pre-fitted scaler for normalization (for validation set)
            random_seed: Random seed for reproducible train/val split
            train_indices: Pre-computed training indices (for consistency)
            val_indices: Pre-computed validation indices (for consistency)
        """
        self.window_size = window_size
        self.train = train

        # Load CSV data
        df = pd.read_csv(csv_path)

        # Separate features and labels
        features = df.iloc[:, :-1].values.astype(np.float32)
        labels = df.iloc[:, -1].values.astype(np.int64)

        # Handle infinite and NaN values in features
        # Replace inf with large finite values and NaN with 0
        features = np.nan_to_num(features, nan=0.0, posinf=1e6, neginf=-1e6)

        # Split data into train/validation using sklearn's train_test_split
        if train_indices is None or val_indices is None:
            # Create new split using sklearn's train_test_split
            # This ensures stratified split and proper randomization
            train_indices, val_indices = train_test_split(
                np.arange(len(features)),
                test_size=(1 - train_ratio),
                random_state=random_seed,
                stratify=labels  # Ensure balanced class distribution in both splits
            )

        # Store indices for reuse
        self.train_indices = train_indices
        self.val_indices = val_indices

        if train:
            features = features[train_indices]
            labels = labels[train_indices]
        else:
            features = features[val_indices]
            labels = labels[val_indices]

        # Normalize features using sklearn's MinMaxScaler
        if scaler is None:
            self.scaler = MinMaxScaler()
            features = self.scaler.fit_transform(features)
        else:
            self.scaler = scaler
            features = self.scaler.transform(features)

        # Create sliding windows
        self.data, self.targets = self._create_windows(features, labels)

    def _create_windows(self, features, labels):
        """
        Create sliding windows from the data based on sequence length.

        Args:
            features: Input features of shape (N, f) where N is samples, f is features
            labels: Input labels of shape (N,)

        Returns:
            windowed_features: Shape (N', T, f) where T is sequence length
            windowed_labels: Shape (N',) corresponding labels
        """
        sequence_length = self.window_size  # Use window_size as sequence length

        if sequence_length == 1:
            # Sequence length = 1: Add dimension 1 to 2D data, labels remain unchanged
            # Shape: (N, f) -> (N, 1, f)
            features_expanded = features[:, np.newaxis, :]  # Add dimension at position 1
            return torch.FloatTensor(features_expanded), torch.LongTensor(labels)

        else:
            # Sequence length > 1: Perform sliding window with step size 1
            # Shape: (N, f) -> (N-T+1, T, f)
            windowed_data = []
            windowed_labels = []

            for i in range(len(features) - sequence_length + 1):
                # Create window of sequence_length consecutive samples
                window = features[i:i + sequence_length]  # Shape: (T, f)
                # Use label of the last sample in the window for prediction
                label = labels[i + sequence_length - 1]
                windowed_data.append(window)
                windowed_labels.append(label)

            windowed_data = np.array(windowed_data)  # Shape: (N-T+1, T, f)
            windowed_labels = np.array(windowed_labels)  # Shape: (N-T+1,)

            return torch.FloatTensor(windowed_data), torch.LongTensor(windowed_labels)

    def __len__(self):
        return len(self.data)

    def __getitem__(self, idx):
        return self.data[idx], self.targets[idx]

def set_random_seed(seed=42):
    """
    Set random seed for reproducibility across different libraries.

    Args:
        seed: Random seed value (default: 42)
    """
    # Python random module
    random.seed(seed)

    # NumPy random seed
    np.random.seed(seed)

    # PyTorch random seed
    torch.manual_seed(seed)

    # PyTorch CUDA random seed (for GPU reproducibility)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)  # For multi-GPU setups

        # Additional settings for deterministic behavior on GPU
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

args = args_parser()

# Import models based on pruning mode
if args.disable_pruning:
    print("=== IMPORTING STANDARD MODELS ===")
    from src.models.models import *
    print("Loaded models from src.models.models (standard implementation)")
else:
    print("=== IMPORTING PRUNING-ENABLED MODELS ===")
    from src.models.unstructured import *
    print("Loaded models from src.models.unstructured (supports pruning)")

# Set random seed for reproducibility
set_random_seed(args.seed)

args.device = torch.device('cuda:{}'.format(args.gpu) if torch.cuda.is_available() else 'cpu')

# Display device information
print(f"\n🖥️  DEVICE INFORMATION:")
print(f"   Device: {args.device}")
if args.device.type == 'cuda':
    print(f"   GPU ID: {args.gpu}")
    print(f"   GPU Name: {torch.cuda.get_device_name(args.gpu)}")
    print(f"   GPU Memory: {torch.cuda.get_device_properties(args.gpu).total_memory / 1024**3:.1f} GB")
    print(f"   CUDA Available: ✅ Yes")
else:
    print(f"   CUDA Available: ❌ No")
    print(f"   Using CPU for computation")

torch.cuda.set_device(args.gpu) ## Setting cuda on GPU
## Data partitioning section

if args.dataset == 'carh':
    csv_path = 'src/RawData/carh.csv'
    window_size = args.window_size

    # Create training dataset
    train_dataset = CSVDataset(csv_path, window_size=window_size, train=True, train_ratio=0.7)

    # Create validation dataset using the same scaler and split indices as training
    test_dataset = CSVDataset(csv_path, window_size=window_size, train=False, train_ratio=0.7,
                             scaler=train_dataset.scaler,
                             train_indices=train_dataset.train_indices,
                             val_indices=train_dataset.val_indices)

    nclass_carh = args.nclass
    nsamples_carh = args.nsample_pc

    if args.noniid:
        if args.shard:
            print(f'--CarH Non-IID-- {args.nclass} random Shards, Sample per shard {args.nsample_pc}')
            user_groups_train, user_groups_test = noniid_shard(args.dataset, train_dataset, test_dataset,
                            args.num_users, nclass_carh, nsamples_carh, args.split_test)

        elif args.label:
            print(f'--CarH Non-IID-- {args.nclass} random Label, Sample per label {args.nsample_pc}')
            user_groups_train, user_groups_test = \
            noniid_label(args.dataset, train_dataset, test_dataset, args.num_users, nclass_carh,
                                 nsamples_carh, args.split_test)

        elif args.dirichlet:
            alpha = args.alpha
            print(f'--CarH Non-IID-- Dirichlet distribution, alpha={alpha}')
            user_groups_train, user_groups_test = \
            noniid_dirichlet(args.dataset, train_dataset, test_dataset, args.num_users, alpha, args.split_test)

        else:
            exit('Error: unrecognized partitioning type')
    else:
        print(f'--CarH IID-- Split Test {args.split_test}')
        user_groups_train, user_groups_test = \
        iid(args.dataset, train_dataset, test_dataset, args.num_users, args.split_test)
            
elif args.dataset == 'cicids2017':
    csv_path = 'src/RawData/cicids2017.csv'
    window_size = args.window_size

    # Create training dataset
    train_dataset = CSVDataset(csv_path, window_size=window_size, train=True, train_ratio=0.7)

    # Create validation dataset using the same scaler and split indices as training
    test_dataset = CSVDataset(csv_path, window_size=window_size, train=False, train_ratio=0.7,
                             scaler=train_dataset.scaler,
                             train_indices=train_dataset.train_indices,
                             val_indices=train_dataset.val_indices)

    nclass_cicids2017 = args.nclass
    nsamples_cicids2017 = args.nsample_pc

    if args.noniid:
        if args.shard:
            print(f'--CICIDS2017 Non-IID-- {args.nclass} random Shards, Sample per shard {args.nsample_pc}')
            user_groups_train, user_groups_test = noniid_shard(args.dataset, train_dataset, test_dataset,
                        args.num_users, nclass_cicids2017, nsamples_cicids2017, args.split_test)

        elif args.label:
            print(f'--CICIDS2017 Non-IID-- {args.nclass} random Labels, Sample per label {args.nsample_pc}')
            user_groups_train, user_groups_test = \
            noniid_label(args.dataset, train_dataset, test_dataset, args.num_users, nclass_cicids2017,
                                 nsamples_cicids2017, args.split_test)

        elif args.dirichlet:
            alpha = args.alpha
            print(f'--CICIDS2017 Non-IID-- Dirichlet distribution, alpha={alpha}')
            user_groups_train, user_groups_test = \
            noniid_dirichlet(args.dataset, train_dataset, test_dataset, args.num_users, alpha, args.split_test)

        else:
            exit('Error: unrecognized partitioning type')
    else:
        print(f'--CICIDS2017 IID-- Split Test {args.split_test}')
        user_groups_train, user_groups_test = \
        iid(args.dataset, train_dataset, test_dataset, args.num_users, args.split_test)
            
elif args.dataset == 'toniot':
    csv_path = 'src/RawData/toniot.csv'
    window_size = args.window_size

    # Create training dataset
    train_dataset = CSVDataset(csv_path, window_size=window_size, train=True, train_ratio=0.7)

    # Create validation dataset using the same scaler and split indices as training
    test_dataset = CSVDataset(csv_path, window_size=window_size, train=False, train_ratio=0.7,
                             scaler=train_dataset.scaler,
                             train_indices=train_dataset.train_indices,
                             val_indices=train_dataset.val_indices)

    nclass_toniot = args.nclass
    nsamples_toniot = args.nsample_pc

    if args.noniid:
        if args.shard:
            print(f'--ToNIoT Non-IID-- {args.nclass} random Shards, Sample per shard {args.nsample_pc}')
            user_groups_train, user_groups_test = noniid_shard(args.dataset, train_dataset, test_dataset,
                            args.num_users, nclass_toniot, nsamples_toniot, args.split_test)
        elif args.label:
            print(f'--ToNIoT Non-IID-- {args.nclass} random Labels, Sample per label {args.nsample_pc}')
            user_groups_train, user_groups_test = \
            noniid_label(args.dataset, train_dataset, test_dataset, args.num_users, nclass_toniot,
                                 nsamples_toniot, args.split_test)

        elif args.dirichlet:
            alpha = args.alpha
            print(f'--ToNIoT Non-IID-- Dirichlet distribution, alpha={alpha}')
            user_groups_train, user_groups_test = \
            noniid_dirichlet(args.dataset, train_dataset, test_dataset, args.num_users, alpha, args.split_test)

        else:
            exit('Error: unrecognized partitioning type')
    else:
        print(f'--ToNIoT IID-- Split Test {args.split_test}')
        user_groups_train, user_groups_test = \
        iid(args.dataset, train_dataset, test_dataset, args.num_users, args.split_test)

else:
    exit(f'Error: Unsupported dataset {args.dataset}. Supported datasets: carh, cicids2017, toniot')

def print_client_data_info(user_groups_train, user_groups_test, train_dataset, test_dataset, num_users, dataset_name):
    """
    Print detailed information about data distribution across clients.

    Args:
        user_groups_train: Dictionary mapping client_id to train data indices
        user_groups_test: Dictionary mapping client_id to test data indices
        train_dataset: Training dataset
        test_dataset: Test dataset
        num_users: Number of clients
        dataset_name: Name of the dataset
    """
    print(f"\n{'='*80}")
    print(f"📊 CLIENT DATA DISTRIBUTION ANALYSIS - {dataset_name.upper()}")
    print(f"{'='*80}")

    # Extract targets
    train_targets = np.array(train_dataset.targets)
    test_targets = np.array(test_dataset.targets)

    # Get unique classes
    unique_classes = sorted(set(train_targets))
    num_classes = len(unique_classes)

    print(f"📈 Dataset Overview:")
    print(f"   Total Training Samples: {len(train_dataset):,}")
    print(f"   Total Test Samples: {len(test_dataset):,}")
    print(f"   Number of Classes: {num_classes}")
    print(f"   Classes: {unique_classes}")
    print(f"   Number of Clients: {num_users}")

    # Calculate overall class distribution
    train_class_counts = {cls: np.sum(train_targets == cls) for cls in unique_classes}
    test_class_counts = {cls: np.sum(test_targets == cls) for cls in unique_classes}

    print(f"\n📊 Overall Class Distribution:")
    print(f"   Training: {train_class_counts}")
    print(f"   Test: {test_class_counts}")

    # Analyze each client
    print(f"\n👥 Per-Client Data Analysis:")
    print(f"{'Client':<8} {'Train':<8} {'Test':<8} {'Train Classes':<20} {'Test Classes':<20} {'Train Distribution':<30} {'Test Distribution':<30}")
    print(f"{'-'*8} {'-'*8} {'-'*8} {'-'*20} {'-'*20} {'-'*30} {'-'*30}")

    total_train_samples = 0
    total_test_samples = 0

    for client_id in range(num_users):
        # Get client's data indices
        train_indices = user_groups_train[client_id]
        test_indices = user_groups_test[client_id]

        # Get client's labels
        client_train_labels = train_targets[train_indices]
        client_test_labels = test_targets[test_indices]

        # Count samples
        train_count = len(client_train_labels)
        test_count = len(client_test_labels)

        total_train_samples += train_count
        total_test_samples += test_count

        # Get unique classes for this client
        train_classes = sorted(set(client_train_labels))
        test_classes = sorted(set(client_test_labels))

        # Calculate class distribution for this client
        train_dist = {cls: np.sum(client_train_labels == cls) for cls in train_classes}
        test_dist = {cls: np.sum(client_test_labels == cls) for cls in test_classes}

        # Format distributions for display
        train_dist_str = ', '.join([f"{cls}:{count}" for cls, count in train_dist.items()])
        test_dist_str = ', '.join([f"{cls}:{count}" for cls, count in test_dist.items()])

        print(f"{client_id:<8} {train_count:<8} {test_count:<8} {str(train_classes):<20} {str(test_classes):<20} {train_dist_str:<30} {test_dist_str:<30}")

    print(f"{'-'*8} {'-'*8} {'-'*8} {'-'*20} {'-'*20} {'-'*30} {'-'*30}")
    print(f"{'Total':<8} {total_train_samples:<8} {total_test_samples:<8}")

    # Calculate statistics
    train_sizes = [len(user_groups_train[i]) for i in range(num_users)]
    test_sizes = [len(user_groups_test[i]) for i in range(num_users)]

    print(f"\n📈 Distribution Statistics:")
    print(f"   Training samples per client:")
    print(f"     Min: {min(train_sizes):,}, Max: {max(train_sizes):,}")
    print(f"     Mean: {np.mean(train_sizes):.1f}, Std: {np.std(train_sizes):.1f}")
    print(f"   Test samples per client:")
    print(f"     Min: {min(test_sizes):,}, Max: {max(test_sizes):,}")
    print(f"     Mean: {np.mean(test_sizes):.1f}, Std: {np.std(test_sizes):.1f}")

    # Calculate class diversity per client
    train_class_diversity = [len(set(train_targets[user_groups_train[i]])) for i in range(num_users)]
    test_class_diversity = [len(set(test_targets[user_groups_test[i]])) for i in range(num_users)]

    print(f"   Classes per client (training):")
    print(f"     Min: {min(train_class_diversity)}, Max: {max(train_class_diversity)}")
    print(f"     Mean: {np.mean(train_class_diversity):.1f}, Std: {np.std(train_class_diversity):.1f}")
    print(f"   Classes per client (test):")
    print(f"     Min: {min(test_class_diversity)}, Max: {max(test_class_diversity)}")
    print(f"     Mean: {np.mean(test_class_diversity):.1f}, Std: {np.std(test_class_diversity):.1f}")

    # Data balance analysis
    train_balance = np.std(train_sizes) / np.mean(train_sizes) if np.mean(train_sizes) > 0 else 0
    test_balance = np.std(test_sizes) / np.mean(test_sizes) if np.mean(test_sizes) > 0 else 0

    print(f"\n⚖️  Data Balance Analysis:")
    print(f"   Training data balance (CV): {train_balance:.3f}")
    print(f"   Test data balance (CV): {test_balance:.3f}")
    print(f"   Balance interpretation: 0.0=perfectly balanced, >0.5=highly imbalanced")

    print(f"{'='*80}\n")

##
## Checking the partitions (total sample and labels for each client)

users_train_labels = {i: [] for i in range(args.num_users)}
users_test_labels = {i: [] for i in range(args.num_users)}

train_targets = np.array(train_dataset.targets)
test_targets = np.array(test_dataset.targets)

for i in range(args.num_users):
    ## Train Data for Each Client 
    train_count_per_client = 0 
    label = train_targets[user_groups_train[i]]
    train_count_per_client += len(label)
    label = set(label)
    users_train_labels[i] = list(label)
    
    # Test Data for Each Client 
    test_count_per_client = 0 
    label = test_targets[user_groups_test[i]]
    test_count_per_client += len(label)
    label = set(label)
    users_test_labels[i] = list(label) 
    
    #print(f'Client: {i}, Train Labels: {users_train_labels[i]}, Test Labels: {users_test_labels[i]},'
          #f' Num Train: {train_count_per_client}, Num Test: {test_count_per_client}')

# Print detailed client data distribution analysis
print_client_data_info(user_groups_train, user_groups_test, train_dataset, test_dataset, args.num_users, args.dataset)

##
# build model
print(f'MODEL: {args.model}, Dataset: {args.dataset}')

users_model = []

# Create intrusion detection models for CSV datasets
if args.model == 'intrusion_detection' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create light CNN + Transformer fusion model
    net_glob = create_intrusion_detection_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        d_model=getattr(args, 'light_d_model', 32),
        nhead=getattr(args, 'light_nhead', 1),
        dropout=args.model_dropout,
        hidden_dim=getattr(args, 'light_hidden_dim', 64),
        cnn_channels=args.cnn_channels
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_intrusion_detection_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            d_model=getattr(args, 'light_d_model', 32),
            nhead=getattr(args, 'light_nhead', 1),
            dropout=args.model_dropout,
            hidden_dim=getattr(args, 'light_hidden_dim', 64),
            cnn_channels=args.cnn_channels
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# GRU model for sequence-based intrusion detection
elif args.model == 'gru' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create GRU model using the convenience function
    net_glob = create_gru_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        dim_hidden=64
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_gru_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=64
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# LSTM model for sequence-based intrusion detection
elif args.model == 'lstm' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create LSTM model using the convenience function
    net_glob = create_lstm_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        dim_hidden=128
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_lstm_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=128
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# Transformer-only model for intrusion detection
elif args.model == 'transformer_only' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create Transformer-only model using the convenience function
    net_glob = create_transformer_only_model(
        dataset_name=args.dataset,
        input_dim=input_dim,
        seq_len=seq_len,
        transformer_layers=getattr(args, 'transformer_only_layers', args.transformer_layers),
        d_model=getattr(args, 'transformer_only_d_model', args.d_model),
        nhead=getattr(args, 'transformer_only_nhead', args.nhead),
        dropout=args.model_dropout,
        hidden_dim=getattr(args, 'transformer_only_hidden_dim', args.hidden_dim)
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_transformer_only_model(
            dataset_name=args.dataset,
            input_dim=input_dim,
            seq_len=seq_len,
            transformer_layers=getattr(args, 'transformer_only_layers', args.transformer_layers),
            d_model=getattr(args, 'transformer_only_d_model', args.d_model),
            nhead=getattr(args, 'transformer_only_nhead', args.nhead),
            dropout=args.model_dropout,
            hidden_dim=getattr(args, 'transformer_only_hidden_dim', args.hidden_dim)
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# CNN-only model for intrusion detection
elif args.model == 'cnn_only' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]
    seq_len = args.window_size

    # Create CNN-only model using the convenience function (EdgeCNN style)
    net_glob = create_cnn_only_model(
        dataset_name=args.dataset,
        input_dim=input_dim
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_cnn_only_model(
            dataset_name=args.dataset,
            input_dim=input_dim
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# EdgeCNN model for intrusion detection (edge computing optimized)
elif args.model == 'edge_cnn' and args.dataset in ['carh', 'cicids2017', 'toniot']:
    # Get dataset-specific parameters
    dataset_features = {'carh': 11, 'cicids2017': 78, 'toniot': 42}
    input_dim = dataset_features[args.dataset]

    # Create EdgeCNN model using the convenience function
    net_glob = create_edge_cnn_model(
        dataset_name=args.dataset,
    ).to(args.device)

    net_glob.apply(weight_init)

    # Create models for each user
    users_model = []
    for _ in range(args.num_users):
        user_model = create_edge_cnn_model(
            dataset_name=args.dataset,
        ).to(args.device)
        user_model.apply(weight_init)
        users_model.append(user_model)

# Keep original models for backward compatibility
elif args.model == 'lenet5' and args.dataset == 'cifar10':
    net_glob = LeNet5Cifar10().to(args.device)
    net_glob.apply(weight_init)
    users_model = [LeNet5Cifar10().to(args.device).apply(weight_init) for _ in range(args.num_users)]
elif args.model == 'lenet5' and args.dataset == 'cifar100':
    net_glob = LeNet5Cifar100().to(args.device)
    net_glob.apply(weight_init)
    users_model = [LeNet5Cifar100().to(args.device).apply(weight_init) for _ in range(args.num_users)]
elif args.model == 'lenet5' and args.dataset == 'mnist':
    net_glob = LeNet5Mnist().to(args.device)
    net_glob.apply(weight_init)
    users_model = [LeNet5Mnist().to(args.device).apply(weight_init) for _ in range(args.num_users)]
else:
    exit(f'Error: Unsupported model-dataset combination: {args.model}-{args.dataset}')

if args.load_initial:
    initial_state_dict = torch.load(args.load_initial)
    net_glob.load_state_dict(initial_state_dict)

initial_state_dict = copy.deepcopy(net_glob.state_dict())
server_state_dict = copy.deepcopy(net_glob.state_dict())

for i in range(args.num_users):
    users_model[i].load_state_dict(initial_state_dict)

def safe_model_copy(original_model, device):
    # """
    # Safely create a copy of a PyTorch model to avoid deepcopy issues.

    # Uses clone().detach() method for efficient tensor copying without serialization overhead.
    # Automatically detects model type and extracts necessary constructor parameters.

    # Args:
    #     original_model: The original PyTorch model to copy
    #     device: The device to place the copied model on

    # Returns:
    #     A new model instance with the same architecture and state_dict as the original
    # """
    # # 方式A：深拷贝 state_dict（并 clone tensor 防止共享）
    # # Clone and detach all tensors in state_dict to ensure complete independence
    sd = {k: v.clone().detach() for k, v in original_model.state_dict().items()}

    # Get model class name to determine constructor parameters
    model_class_name = type(original_model).__name__

    # Create new model instance with proper parameters based on model type
    if 'IntrusionDetection' in model_class_name:
        # Extract parameters for light CNN + Transformer fusion model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        d_model = getattr(original_model, 'd_model', 32)
        nhead = getattr(original_model, 'nhead', 1)
        dropout = getattr(original_model, 'dropout', 0.3)
        hidden_dim = getattr(original_model, 'hidden_dim', 64)
        cnn_channels = getattr(original_model, 'cnn_channels', 32)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        from src.models.models import create_intrusion_detection_model
        new_model = create_intrusion_detection_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            d_model=d_model,
            nhead=nhead,
            dropout=dropout,
            hidden_dim=hidden_dim,
            cnn_channels=cnn_channels  # 从原始模型中提取
        )

    elif 'GRU' in model_class_name:
        # Extract parameters for GRU model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        dim_hidden = getattr(original_model, 'dim_hidden', 64)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        from src.models.models import create_gru_model
        new_model = create_gru_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=dim_hidden
        )

    elif 'LSTM' in model_class_name:
        # Extract parameters for LSTM model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        dim_hidden = getattr(original_model, 'dim_hidden', 128)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        from src.models.models import create_lstm_model
        new_model = create_lstm_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            dim_hidden=dim_hidden
        )

    elif 'LeNet5' in model_class_name:
        # LeNet5 models don't need parameters
        if 'Cifar10' in model_class_name:
            from src.models.models import LeNet5Cifar10
            new_model = LeNet5Cifar10()
        elif 'Cifar100' in model_class_name:
            from src.models.models import LeNet5Cifar100
            new_model = LeNet5Cifar100()
        elif 'Mnist' in model_class_name:
            from src.models.models import LeNet5Mnist
            new_model = LeNet5Mnist()
        else:
            raise ValueError(f"Unknown LeNet5 variant: {model_class_name}")

    elif 'TransformerOnly' in model_class_name:
        # Extract parameters for TransformerOnly model
        input_dim = getattr(original_model, 'input_dim', 78)
        seq_len = getattr(original_model, 'seq_len', 1)
        transformer_layers = getattr(original_model, 'transformer_layers', 2)  # ✅ FIXED: Match create_transformer_only_model
        d_model = getattr(original_model, 'd_model', 64)  # ✅ FIXED: Match create_transformer_only_model
        nhead = getattr(original_model, 'nhead', 4)  # ✅ FIXED: Match create_transformer_only_model
        dropout = getattr(original_model, 'dropout', 0.3)  # ✅ FIXED: Match create_transformer_only_model
        hidden_dim = getattr(original_model, 'hidden_dim', 128)  # ✅ FIXED: Match create_transformer_only_model

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        from src.models.models import create_transformer_only_model
        new_model = create_transformer_only_model(
            dataset_name=dataset_name,
            input_dim=input_dim,
            seq_len=seq_len,
            transformer_layers=transformer_layers,
            d_model=d_model,
            nhead=nhead,
            dropout=dropout,
            hidden_dim=hidden_dim
        )

    elif 'CNNOnly' in model_class_name:
        # Extract parameters for CNNOnly model (EdgeCNN style)
        input_dim = getattr(original_model, 'input_dim', 78)

        # Determine dataset name from input_dim
        dataset_map = {11: 'carh', 78: 'cicids2017', 42: 'toniot'}
        dataset_name = dataset_map.get(input_dim, 'cicids2017')

        from src.models.models import create_cnn_only_model
        new_model = create_cnn_only_model(
            dataset_name=dataset_name,
            input_dim=input_dim
        )

    elif model_class_name == 'EdgeCNN':
        # Extract parameters for EdgeCNN model
        n_classes = getattr(original_model, 'dense')[-1].out_features  # Get number of classes from last layer

        # Create new EdgeCNN model with correct parameters
        from src.models.models import EdgeCNN
        new_model = EdgeCNN(
            n_classes=n_classes
        )

    else:
        # For unknown model types, try direct instantiation as fallback
        try:
            new_model = type(original_model)()
        except Exception as e:
            raise ValueError(f"Cannot create model copy for unknown type {model_class_name}: {e}")

    # Move to target device first
    new_model = new_model.to(device)

    # Load the cloned state_dict (TokenLearner layers should exist now)
    new_model.load_state_dict(sd)

    return new_model

## Create clients based on pruning mode
clients = []

if not args.disable_pruning:
    print("=== PRUNING MODE ENABLED ===")
    print("Using Client_Sub_Un with pruning functionality")

    # Initialize pruning masks
    mask_init = make_init_mask(net_glob)

    for idx in range(args.num_users):
        # Use safe model copy to avoid deepcopy issues
        user_model_copy = safe_model_copy(users_model[idx], args.device)

        clients.append(Client_Sub_Un(idx, user_model_copy, args.local_bs, args.local_ep,
                   args.lr, args.device, copy.deepcopy(mask_init),
                   args.pruning_target, train_dataset, user_groups_train[idx],
                   test_dataset, user_groups_test[idx],
                   beta1=args.beta1, beta2=args.beta2, eps=args.eps, weight_decay=args.weight_decay))
else:
    print("=== STANDARD FEDAVG MODE ENABLED ===")
    print("Using Client_Standard without pruning functionality")

    for idx in range(args.num_users):
        # Use safe model copy to avoid deepcopy issues with intrusion_detection model
        user_model_copy = safe_model_copy(users_model[idx], args.device)

        # Test: Use copy.deepcopy to see if it causes NaN issues
        clients.append(Client_Standard(idx, user_model_copy , args.local_bs, args.local_ep,  #copy.deepcopy(users_model[idx])
                   args.lr, args.device, train_dataset, user_groups_train[idx],
                   test_dataset, user_groups_test[idx],
                   beta1=args.beta1, beta2=args.beta2, eps=args.eps, weight_decay=args.weight_decay))
    
## 
loss_train = []

init_tracc_pr = []  # initial train accuracy for each round 
final_tracc_pr = [] # final train accuracy for each round 

init_tacc_pr = []  # initial test accuarcy for each round 
final_tacc_pr = [] # final test accuracy for each round

init_tloss_pr = []  # initial test loss for each round 
final_tloss_pr = [] # final test loss for each round 

clients_best_acc = [0 for _ in range(args.num_users)]
w_locals, loss_locals = [], []

# Initialize tracking variables based on mode
if not args.disable_pruning:
    masks = []
    ckp_avg_pruning = []
    ckp_avg_best_tacc_before = []
    ckp_avg_best_tacc_after = []

init_local_tacc = []       # initial local test accuracy at each round
final_local_tacc = []  # final local test accuracy at each round

init_local_tloss = []      # initial local test loss at each round
final_local_tloss = []     # final local test loss at each round

ckp_avg_tacc = []

# Create tqdm progress bar for training rounds
progress_bar = tqdm(range(args.rounds), desc="🚀 Training Progress", unit="round")

for iteration in progress_bar:
        
    m = max(int(args.frac * args.num_users), 1)
    idxs_users = np.random.choice(range(args.num_users), m, replace=False)
    
    if args.is_print:
        print(f'###### ROUND {iteration+1} ######')
        print(f'Clients {idxs_users}')
    
    for idx in idxs_users:

        # Update client model with global model (different for each mode)
        if iteration+1 > 1:
            if not args.disable_pruning:
                # Pruning mode: use Sub_FedAvg_U_initial
                dic = Sub_FedAvg_U_initial(copy.deepcopy(clients[idx].get_mask()),
                                         copy.deepcopy(clients[idx].get_net()), server_state_dict)
                clients[idx].set_state_dict(dic)
            else:
                # Standard mode: directly set state dict
                clients[idx].set_state_dict(copy.deepcopy(server_state_dict))

        # Evaluate before training
        loss, acc = clients[idx].eval_test()
        init_local_tacc.append(acc)
        init_local_tloss.append(loss)

        # Train client (different parameters for each mode)
        if not args.disable_pruning:
            # Pruning mode: pass pruning parameters
            loss = clients[idx].train(args.pruning_percent, args.dist_thresh, args.acc_thresh, is_print=False)
            # Collect masks for pruning mode
            masks.append(copy.deepcopy(clients[idx].get_mask()))
        else:
            # Standard mode: simple training
            loss = clients[idx].train(is_print=False)
            # Update client statistics
            clients[idx].increment_count()

        # Collect model weights and losses
        w_locals.append(copy.deepcopy(clients[idx].get_state_dict()))
        loss_locals.append(copy.deepcopy(loss))

        # Evaluate after training
        loss, acc = clients[idx].eval_test()

        # Update best accuracy
        if acc > clients_best_acc[idx]:
            clients_best_acc[idx] = acc

        # Update client's best accuracy for standard mode
        if args.disable_pruning:
            clients[idx].update_best_acc(acc)
  
        final_local_tacc.append(acc)
        final_local_tloss.append(loss)           
        
    # Aggregate models based on mode
    if not args.disable_pruning:
        # Pruning mode: use Sub_FedAVG_U with masks
        server_state_dict = Sub_FedAVG_U(server_state_dict, w_locals, masks)
    else:
        # Standard mode: use FedAvg with proper parameters
        # Option 1: Use full FedAvg_standard with global model and sample counts
        # Calculate sample counts for weighted averaging
        sample_counts = [len(user_groups_train[idx]) for idx in idxs_users]
        server_state_dict = FedAvg_standard(server_state_dict, w_locals, sample_counts)
    
    # print loss
    loss_avg = sum(loss_locals) / len(loss_locals)
    avg_init_tloss = sum(init_local_tloss) / len(init_local_tloss)
    avg_init_tacc = sum(init_local_tacc) / len(init_local_tacc)
    avg_final_tloss = sum(final_local_tloss) / len(final_local_tloss)
    avg_final_tacc = sum(final_local_tacc) / len(final_local_tacc)
    
    if args.is_print:    
        print('## END OF ROUND ##')
        template = "Round {}, Average Train loss {:.3f}"
        print(template.format(iteration+1, loss_avg))

        template = "AVG Init Test Loss: {:.3f}, AVG Init Test Acc: {:.3f}"
        print(template.format(avg_init_tloss, avg_init_tacc))

        template = "AVG Final Test Loss: {:.3f}, AVG Final Test Acc: {:.3f}"
        print(template.format(avg_final_tloss, avg_final_tacc))

    if (iteration + 1)%args.print_freq == 0:
        print('--- PRINTING ALL CLIENTS STATUS ---')
        current_acc = []
        best_acc_list = []

        if not args.disable_pruning:
            # Pruning mode: collect pruning statistics
            pruning_state = []
            best_acc_before_pruning = []

            for k in range(args.num_users):
                best_acc_before_pruning.append(clients[k].get_best_acc())
                pruning_state.append(clients[k].get_pruning())
                loss, acc = clients[k].eval_test()
                current_acc.append(acc)
                best_acc_list.append(clients[k].get_best_acc())

                template = ("Client {:3d}, labels {}, count {}, pruning_state {:3.3f}, "
                           "best_acc_before_pruning {:3.3f}, after_pruning {:3.3f}, current_acc {:3.3f} \n")

                print(template.format(k, users_train_labels[k], clients[k].get_count(), pruning_state[-1],
                                     best_acc_before_pruning[-1], clients_best_acc[k], current_acc[-1]))

            template = ("Round {:1d}, Avg Pruning {:3.3f}, Avg current_acc {:3.3f}, "
                         "Avg best_acc_before_pruning {:3.3f}, after_pruning {:3.3f}")

            print(template.format(iteration+1, np.mean(pruning_state), np.mean(current_acc),
                                  np.mean(best_acc_before_pruning), np.mean(clients_best_acc)))

            # Store pruning statistics
            ckp_avg_pruning.append(np.mean(pruning_state))
            ckp_avg_best_tacc_before.append(np.mean(best_acc_before_pruning))
            ckp_avg_best_tacc_after.append(np.mean(clients_best_acc))

            # 找到具有最大 current_acc 的客户端并计算详细指标 (Pruning Mode)
            if current_acc:  # 确保有数据
                best_client_idx = np.argmax(current_acc)
                best_client_acc = current_acc[best_client_idx]

                print(f"\n🏆 Best Client Analysis (Round {iteration+1}, Pruning Mode):")
                print(f"   Client ID: {best_client_idx}")
                print(f"   Current Accuracy: {best_client_acc:.4f}")
                print(f"   Pruning State: {pruning_state[best_client_idx]:.4f}")

                # 计算详细指标
                try:
                    # 获取数据集信息
                    if args.dataset == 'cicids2017':
                        num_classes = 8
                    elif args.dataset == 'carh':
                        num_classes = 5
                    elif args.dataset == 'toniot':
                        num_classes = 10
                    else:
                        num_classes = 8  # 默认值

                    # 获取客户端的测试数据
                    client_test_data = clients[best_client_idx].ldr_test

                    # 计算详细指标
                    detailed_metrics = calculate_detailed_metrics(
                        clients[best_client_idx],
                        client_test_data,
                        args.device,
                        num_classes
                    )

                    # 打印详细指标
                    print(f"\n📊 Detailed Metrics for Best Client {best_client_idx} (Pruning Mode):")
                    print(f"   Accuracy: {detailed_metrics['accuracy']:.4f}")
                    print(f"   Precision (Macro): {detailed_metrics['precision_macro']:.4f}")
                    print(f"   Recall (Macro): {detailed_metrics['recall_macro']:.4f}")
                    print(f"   F1-Score (Macro): {detailed_metrics['f1_macro']:.4f}")
                    print(f"   False Alarm Rate (Avg): {detailed_metrics['avg_false_alarm_rate']:.4f}")

                    print(f"\n📈 Per-Class Metrics:")
                    for i in range(num_classes):
                        if i < len(detailed_metrics['precision_per_class']):
                            print(f"   Class {i}: Precision={detailed_metrics['precision_per_class'][i]:.4f}, "
                                  f"Recall={detailed_metrics['recall_per_class'][i]:.4f}, "
                                  f"F1={detailed_metrics['f1_per_class'][i]:.4f}, "
                                  f"FAR={detailed_metrics['false_alarm_rates'][i]:.4f}")

                    print(f"\n🔢 Confusion Matrix:")
                    print(detailed_metrics['confusion_matrix'])

                    # 存储详细指标到全局变量
                    if not hasattr(calculate_detailed_metrics, 'best_client_metrics_pruning'):
                        calculate_detailed_metrics.best_client_metrics_pruning = []

                    calculate_detailed_metrics.best_client_metrics_pruning.append({
                        'round': iteration + 1,
                        'client_id': best_client_idx,
                        'pruning_state': pruning_state[best_client_idx],
                        'metrics': detailed_metrics
                    })

                except Exception as e:
                    print(f"❌ Error calculating detailed metrics: {e}")
                    import traceback
                    traceback.print_exc()

        else:
            # Standard mode: simpler statistics
            for k in range(args.num_users):
                loss, acc = clients[k].eval_test()
                current_acc.append(acc)
                best_acc_list.append(clients[k].get_best_acc())

                template = ("Client {:3d}, labels {}, count {}, "
                           "best_acc {:3.3f}, current_acc {:3.3f} \n")

                print(template.format(k, users_train_labels[k], clients[k].get_count(),
                                     clients[k].get_best_acc(), current_acc[-1]))

            template = ("Round {:1d}, Avg current_acc {:3.3f}, Avg best_acc {:3.3f}")

            print(template.format(iteration+1, np.mean(current_acc), np.mean(best_acc_list)))

            # 找到具有最大 current_acc 的客户端并计算详细指标
            if current_acc:  # 确保有数据
                best_client_idx = np.argmax(current_acc)
                best_client_acc = current_acc[best_client_idx]

                print(f"\n🏆 Best Client Analysis (Round {iteration+1}):")
                print(f"   Client ID: {best_client_idx}")
                print(f"   Current Accuracy: {best_client_acc:.4f}")

                # 计算详细指标
                try:
                    # 获取数据集信息
                    if args.dataset == 'cicids2017':
                        num_classes = 8
                    elif args.dataset == 'carh':
                        num_classes = 5
                    elif args.dataset == 'toniot':
                        num_classes = 10
                    else:
                        num_classes = 8  # 默认值

                    # 获取客户端的测试数据
                    client_test_data = clients[best_client_idx].ldr_test

                    # 计算详细指标
                    detailed_metrics = calculate_detailed_metrics(
                        clients[best_client_idx],
                        client_test_data,
                        args.device,
                        num_classes
                    )

                    # 打印详细指标
                    print(f"\n📊 Detailed Metrics for Best Client {best_client_idx}:")
                    print(f"   Accuracy: {detailed_metrics['accuracy']:.4f}")
                    print(f"   Precision (Macro): {detailed_metrics['precision_macro']:.4f}")
                    print(f"   Recall (Macro): {detailed_metrics['recall_macro']:.4f}")
                    print(f"   F1-Score (Macro): {detailed_metrics['f1_macro']:.4f}")
                    print(f"   False Alarm Rate (Avg): {detailed_metrics['avg_false_alarm_rate']:.4f}")

                    print(f"\n📈 Per-Class Metrics:")
                    for i in range(num_classes):
                        if i < len(detailed_metrics['precision_per_class']):
                            print(f"   Class {i}: Precision={detailed_metrics['precision_per_class'][i]:.4f}, "
                                  f"Recall={detailed_metrics['recall_per_class'][i]:.4f}, "
                                  f"F1={detailed_metrics['f1_per_class'][i]:.4f}, "
                                  f"FAR={detailed_metrics['false_alarm_rates'][i]:.4f}")

                    print(f"\n🔢 Confusion Matrix:")
                    print(detailed_metrics['confusion_matrix'])

                    # 存储详细指标到全局变量或文件
                    if not hasattr(calculate_detailed_metrics, 'best_client_metrics'):
                        calculate_detailed_metrics.best_client_metrics = []

                    calculate_detailed_metrics.best_client_metrics.append({
                        'round': iteration + 1,
                        'client_id': best_client_idx,
                        'metrics': detailed_metrics
                    })

                except Exception as e:
                    print(f"❌ Error calculating detailed metrics: {e}")
                    import traceback
                    traceback.print_exc()

        # Store common statistics
        ckp_avg_tacc.append(np.mean(current_acc))
        
    loss_train.append(loss_avg)
    
    init_tacc_pr.append(avg_init_tacc)
    init_tloss_pr.append(avg_init_tloss)
    
    final_tacc_pr.append(avg_final_tacc)
    final_tloss_pr.append(avg_final_tloss)
    
    ## clear the placeholders for the next round
    if not args.disable_pruning:
        masks.clear()
    w_locals.clear()
    loss_locals.clear()
    init_local_tacc.clear()
    init_local_tloss.clear()
    final_local_tacc.clear()
    final_local_tloss.clear()
    
    ## calling garbage collector
    gc.collect()

    # Update progress bar with current round statistics
    progress_bar.set_postfix({
        'Round': f'{iteration+1}/{args.rounds}',
        'Train_Loss': f'{loss_avg:.3f}',
        'Test_Acc': f'{avg_final_tacc:.3f}',
        'Clients': f'{len(idxs_users)}/{args.num_users}'
    })
    
## Printing Final Test and Train ACC / LOSS
test_loss = []
test_acc = []
train_loss = []
train_acc = []

for idx in range(args.num_users):        
    loss, acc = clients[idx].eval_test()
        
    test_loss.append(loss)
    test_acc.append(acc)
    
    loss, acc = clients[idx].eval_train()
    
    train_loss.append(loss)
    train_acc.append(acc)

test_loss = sum(test_loss) / len(test_loss)
test_acc = sum(test_acc) / len(test_acc)

train_loss = sum(train_loss) / len(train_loss)
train_acc = sum(train_acc) / len(train_acc)

print(f'Train Loss: {train_loss}, Test_loss: {test_loss}')
print(f'Train Acc: {train_acc}, Test Acc: {test_acc}')