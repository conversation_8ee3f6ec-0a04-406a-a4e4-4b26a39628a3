import numpy as np
import copy 

import torch 
from torch import nn, optim
import torch.nn.functional as F
from torch.utils.data import DataLoader

from ..data.data import DatasetSplit 

class Client_Standard(object):
    """
    Standard FedAvg client without pruning functionality.
    
    This client implements the standard federated averaging algorithm
    without any model pruning or compression techniques.
    """
    
    def __init__(self, name, model, local_bs, local_ep, lr, device, 
                 train_ds=None, train_idxs=None, test_ds=None, test_idxs=None,
                 beta1=0.9, beta2=0.999, eps=1e-8, weight_decay=1e-4):
        """
        Initialize standard FedAvg client.
        
        Args:
            name: Client identifier
            model: Neural network model
            local_bs: Local batch size
            local_ep: Number of local epochs
            lr: Learning rate
            device: Computing device (CPU/GPU)
            train_ds: Training dataset
            train_idxs: Training data indices for this client
            test_ds: Test dataset
            test_idxs: Test data indices for this client
            beta1: Adam optimizer beta1 parameter
            beta2: Adam optimizer beta2 parameter
            eps: Adam optimizer epsilon parameter
            weight_decay: Weight decay for regularization
        """
        self.name = name 
        self.net = model
        self.local_bs = local_bs
        self.local_ep = local_ep
        self.lr = lr 
        self.device = device 
        self.loss_func = nn.CrossEntropyLoss()
        self.ldr_train = DataLoader(DatasetSplit(train_ds, train_idxs), batch_size=self.local_bs, shuffle=False)
        self.ldr_test = DataLoader(DatasetSplit(test_ds, test_idxs), batch_size=self.local_bs)
        
        # Adam optimizer parameters
        self.beta1 = beta1
        self.beta2 = beta2
        self.eps = eps
        self.weight_decay = weight_decay
        
        # Training statistics
        self.acc_best = 0 
        self.count = 0 
        
    def train(self, is_print=False):
        """
        Standard federated learning training without pruning.
        
        Args:
            is_print: Whether to print training progress
            
        Returns:
            List of epoch losses
        """
        self.net.to(self.device)
        self.net.train()
        
        # Create Adam optimizer
        optimizer = torch.optim.Adam(
            self.net.parameters(),
            lr=self.lr,
            betas=(self.beta1, self.beta2),
            eps=self.eps,
            weight_decay=self.weight_decay
        )
        
        epoch_loss = []
        
        for iteration in range(self.local_ep):
            batch_loss = []
            for batch_idx, (images, labels) in enumerate(self.ldr_train):
                images, labels = images.to(self.device), labels.to(self.device)
                # print("=======================",len(labels))
                
                # Zero gradients
                # self.net.zero_grad()
                optimizer.zero_grad()
                
                # Forward pass
                log_probs = self.net(images)
                # print('============================', log_probs)
                loss = self.loss_func(log_probs, labels)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                # print('==============================', log_probs.size())
                
                batch_loss.append(loss.item())
                
                if is_print and batch_idx % 50 == 0:
                    print('Update Epoch: {} [{}/{} ({:.0f}%)]\tLoss: {:.6f}'.format(
                        iteration, batch_idx * len(images), len(self.ldr_train.dataset),
                        100. * batch_idx / len(self.ldr_train), loss.item()))
            
            epoch_loss.append(sum(batch_loss)/len(batch_loss))
        
        return sum(epoch_loss) / len(epoch_loss)
    
    def eval_test(self):
        """
        Evaluate model on test data.
        
        Returns:
            Tuple of (test_loss, test_accuracy)
        """
        self.net.eval()
        test_loss = 0
        correct = 0
        
        with torch.no_grad():
            for data, target in self.ldr_test:
                data, target = data.to(self.device), target.to(self.device)
                output = self.net(data)
                test_loss += self.loss_func(output, target).item()
                pred = output.data.max(1, keepdim=True)[1]
                correct += pred.eq(target.data.view_as(pred)).long().cpu().sum()

        test_loss /= len(self.ldr_test.dataset)
        accuracy = 100. * correct / len(self.ldr_test.dataset)
        
        return test_loss, accuracy.item()
    
    def get_state_dict(self):
        """Get model state dictionary."""
        return self.net.state_dict()
    
    def set_state_dict(self, state_dict):
        """Set model state dictionary."""
        self.net.load_state_dict(state_dict)
    
    def get_net(self):
        """Get the neural network model."""
        return self.net
    
    def get_best_acc(self):
        """Get best accuracy achieved."""
        return self.acc_best
    
    def get_count(self):
        """Get training count."""
        return self.count
    
    def update_best_acc(self, acc):
        """Update best accuracy."""
        if acc > self.acc_best:
            self.acc_best = acc
    
    def increment_count(self):
        """Increment training count."""
        self.count += 1

    def eval_train(self):
        """
        Evaluate the model on training data.

        Returns:
            tuple: (train_loss, accuracy) where:
                - train_loss: Average loss on training data
                - accuracy: Accuracy percentage on training data
        """
        self.net.to(self.device)
        self.net.eval()
        train_loss = 0
        correct = 0
        with torch.no_grad():
            for data, target in self.ldr_train:
                data, target = data.to(self.device), target.to(self.device)
                output = self.net(data)
                train_loss += F.cross_entropy(output, target, reduction='sum').item()  # sum up batch loss
                pred = output.data.max(1, keepdim=True)[1]  # get the index of the max log-probability
                correct += pred.eq(target.data.view_as(pred)).long().cpu().sum()
        train_loss /= len(self.ldr_train.dataset)
        accuracy = 100. * correct / len(self.ldr_train.dataset)
        return train_loss, accuracy
