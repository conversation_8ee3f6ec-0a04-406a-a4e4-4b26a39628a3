"""
ToNIoT数据集处理模块。
用于读取和预处理ToNIoT数据集，执行标签编码，并提取指定比例的数据。
"""
import os
import pandas as pd
import numpy as np
import traceback
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from typing import Dict, Tuple, List, Any, Optional

def process_toniot_data(data_path: str, sample_ratio: float = 0.1, save_csv: bool = True, output_path: Optional[str] = None, random_seed: int = 42) -> pd.DataFrame:
    """处理ToNIoT数据集。
    
    Args:
        data_path: 数据所在的路径
        sample_ratio: 从原始数据中按类别提取的比例，默认为0.1
        save_csv: 是否保存处理后的数据框到CSV文件，默认为True
        output_path: 输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/toniot.csv
        random_seed: 随机种子，用于确保抽样结果可复现，默认为42
        
    Returns:
        处理后的DataFrame
    """
    print(f"处理ToNIoT数据集，路径: {data_path}")
    print(f"随机种子: {random_seed}")
    
    # 设置随机种子
    np.random.seed(random_seed)
    
    # 设置默认输出路径
    if output_path is None:
        # 获取项目根目录
        root_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../"))
        output_path = os.path.join(root_dir, "flgoasyn/benchmark/RawData/toniot.csv")
    
    # 寻找csv文件
    csv_files = []
    for root, dirs, files in os.walk(data_path):
        for file in files:
            if file.endswith('.csv'):
                csv_files.append(os.path.join(root, file))
    
    if not csv_files:
        raise FileNotFoundError(f"在{data_path}路径下未找到任何csv文件")
    
    print(f"找到{len(csv_files)}个csv文件")
    
    # 读取并合并所有csv文件
    all_data = []
    for file in csv_files:
        try:
            df = pd.read_csv(file, low_memory=False)
            print(f"读取文件 {os.path.basename(file)}, 形状: {df.shape}")
            all_data.append(df)
        except Exception as e:
            print(f"读取文件 {file} 时出错: {str(e)}")
            traceback.print_exc()
    
    if not all_data:
        raise ValueError("没有成功读取任何数据")
    
    # 合并所有数据
    data = pd.concat(all_data, ignore_index=True)
    print(f"合并后数据形状: {data.shape}")
    
    # 删除'label'列（如果存在）
    if 'label' in data.columns:
        data = data.drop(columns=['label'])
        print("删除'label'列")
    
    # 检查'type'列是否存在
    if 'type' not in data.columns:
        raise ValueError("数据中不存在'type'列，请确认数据集格式")
    
    # 转换非数字列为数字
    encoders = {}  # 存储每列的编码器，便于后续使用
    for col in data.columns:
        if col != 'type':  # 不处理类别列
            if data[col].dtype == 'object':
                try:
                    # 尝试直接转换为数值
                    data[col] = pd.to_numeric(data[col], errors='raise')
                    print(f"将列 '{col}' 直接转换为数值类型")
                except Exception as e:
                    # 如果无法直接转换，使用LabelEncoder
                    try:
                        encoder = LabelEncoder()
                        data[col] = encoder.fit_transform(data[col].astype(str))
                        encoders[col] = encoder
                        print(f"将列 '{col}' 使用LabelEncoder映射为整数编码")
                        # 打印编码映射
                        value_mapping = {label: idx for idx, label in enumerate(encoder.classes_)}
                        print(f"  列 '{col}' 的编码映射: {value_mapping}")
                    except Exception as e2:
                        print(f"警告: 无法对列 '{col}' 进行编码: {str(e2)}")
                        # 如果编码也失败，填充为0
                        data[col] = 0
                        print(f"  列 '{col}' 已填充为0")
    
    # 保存编码器信息（可选）
    if encoders and save_csv:
        import pickle
        encoder_path = os.path.join(os.path.dirname(output_path), "toniot_encoders.pkl")
        try:
            with open(encoder_path, 'wb') as f:
                pickle.dump(encoders, f)
            print(f"特征编码器已保存到: {encoder_path}")
        except Exception as e:
            print(f"保存编码器时出错: {str(e)}")
    
    # 按类别提取指定比例的数据
    sampled_data = pd.DataFrame()
    for class_name in data['type'].unique():
        class_data = data[data['type'] == class_name]
        # 计算要提取的样本数
        n_samples = max(1, int(len(class_data) * sample_ratio))
        # 随机抽样
        if len(class_data) > n_samples:
            class_sampled = class_data.sample(n=n_samples, random_state=random_seed)
        else:
            class_sampled = class_data  # 如果数据不足，则使用全部数据
        sampled_data = pd.concat([sampled_data, class_sampled], ignore_index=True)
        print(f"类别 '{class_name}': 原始数据量 {len(class_data)}, 抽样数据量 {len(class_sampled)}")
    
    # 对'type'列进行标签编码
    try:
        # 先使用LabelEncoder获取所有唯一类别
        label_encoder = LabelEncoder()
        original_labels = label_encoder.fit_transform(sampled_data['type'])
        
        # 创建类别映射字典
        class_mapping = {label: idx for idx, label in enumerate(label_encoder.classes_)}
        reverse_mapping = {idx: label for label, idx in class_mapping.items()}
        
        # 检查是否存在normal类别（不区分大小写）
        normal_class = None
        for class_name in label_encoder.classes_:
            if class_name.lower() == 'normal':
                normal_class = class_name
                break
        
        # 如果找到normal类别，创建新的映射确保它为0
        if normal_class is not None:
            normal_idx = class_mapping[normal_class]
            
            # 创建新的映射关系
            new_mapping = {}
            idx_counter = 1  # 从1开始为非normal类别编号
            
            # 将normal类别映射为0
            new_mapping[normal_idx] = 0
            
            # 为其他类别重新分配索引
            for old_idx in range(len(label_encoder.classes_)):
                if old_idx != normal_idx:
                    new_mapping[old_idx] = idx_counter
                    idx_counter += 1
            
            # 应用新映射到标签
            sampled_data['Label'] = [new_mapping[idx] for idx in original_labels]
            
            # 创建更新后的类别到索引的映射用于显示
            updated_class_mapping = {reverse_mapping[old_idx]: new_idx 
                                    for old_idx, new_idx in new_mapping.items()}
            
            print("类别编码映射 (已调整'normal'为0):")
            for class_name, idx in sorted(updated_class_mapping.items(), key=lambda x: x[1]):
                print(f"  {class_name} -> {idx}")
        else:
            # 如果没有找到normal类别，直接使用LabelEncoder的结果
            sampled_data['Label'] = original_labels
            print("类别编码映射 (未找到'normal'类别):")
            for class_name, idx in sorted(class_mapping.items(), key=lambda x: x[1]):
                print(f"  {class_name} -> {idx}")
        
        # 删除原始type列
        sampled_data = sampled_data.drop(columns=['type'])
    except Exception as e:
        print(f"标签编码过程中出错: {str(e)}")
        traceback.print_exc()
        raise
    
    # 处理缺失值
    sampled_data = sampled_data.fillna(0)
    
    if sampled_data.empty:
        raise ValueError("处理后的数据为空")
    
    print(f"处理后的数据形状: {sampled_data.shape}")
    print(f"类别分布:\n{sampled_data['Label'].value_counts()}")
    
    # 保存处理后的数据框到CSV文件
    if save_csv and not sampled_data.empty:
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)
                print(f"创建输出目录: {output_dir}")
            
            # 保存数据框到CSV文件
            sampled_data.to_csv(output_path, index=False)
            print(f"处理后的数据已保存到: {output_path}")
        except Exception as e:
            print(f"保存数据到CSV时出错: {str(e)}")
            traceback.print_exc()
    
    return sampled_data

if __name__ == "__main__":
    # 如果直接运行此脚本，处理数据并保存到CSV
    import argparse
    
    parser = argparse.ArgumentParser(description='处理ToNIoT数据集并保存为CSV')
    parser.add_argument('--data_path', type=str, required=True, help='数据所在的路径')
    parser.add_argument('--output_path', type=str, default=None, 
                        help='输出CSV文件的路径，默认为flgoasyn/benchmark/RawData/toniot.csv')
    parser.add_argument('--sample_ratio', type=float, default=0.3, 
                        help='从原始数据中按类别提取的比例，默认为0.1')
    parser.add_argument('--random_seed', type=int, default=42,
                        help='随机种子，用于确保抽样结果可复现，默认为42')
    
    args = parser.parse_args()
    
    # 处理数据并保存
    process_toniot_data(
        data_path=args.data_path,
        sample_ratio=args.sample_ratio,
        save_csv=True,
        output_path=args.output_path,
        random_seed=args.random_seed
    )
    
    print("数据处理完成！") 