import numpy as np
import math
import torch
from torch import nn
import torch.nn.functional as F
import torch.nn.init as init

## Intrusion Detection Models for UnStructured Pruning
## This module implements CNN + Transformer fusion architecture for network intrusion detection
## Designed specifically for CSV-based security datasets (CarH, CICIDS2017, ToNIoT)
## Compatible with federated learning frameworks and supports variable sequence lengths
## Dropout and BatchNorm are commented out to support pruning operations

# Custom PyTorch layers converted from TensorFlow

class CustomMultiheadAttention(nn.Module):
    """
    自定义多头注意力机制
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super(CustomMultiheadAttention, self).__init__()
        assert d_model % num_heads == 0

        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        self.scale = math.sqrt(self.head_dim)

        # 线性投影层
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)

        # self.dropout = nn.Dropout(dropout)  ## if dropout uncomment this line!

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """初始化权重以提高数值稳定性"""
        nn.init.xavier_uniform_(self.q_linear.weight.data, gain=0.1)
        nn.init.xavier_uniform_(self.k_linear.weight.data, gain=0.1)
        nn.init.xavier_uniform_(self.v_linear.weight.data, gain=0.1)
        nn.init.xavier_uniform_(self.out_linear.weight.data, gain=0.1)

        if self.q_linear.bias is not None:
            nn.init.constant_(self.q_linear.bias.data, 0.0)
        if self.k_linear.bias is not None:
            nn.init.constant_(self.k_linear.bias.data, 0.0)
        if self.v_linear.bias is not None:
            nn.init.constant_(self.v_linear.bias.data, 0.0)
        if self.out_linear.bias is not None:
            nn.init.constant_(self.out_linear.bias.data, 0.0)

    def forward(self, query, key, value, attn_mask=None, key_padding_mask=None):
        batch_size, seq_len, _ = query.shape

        # 线性投影
        Q = self.q_linear(query)  # (batch_size, seq_len, d_model)
        K = self.k_linear(key)    # (batch_size, seq_len, d_model)
        V = self.v_linear(value)  # (batch_size, seq_len, d_model)

        # 重塑为多头格式
        Q = Q.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        K = K.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)
        V = V.view(batch_size, seq_len, self.num_heads, self.head_dim).transpose(1, 2)

        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale

        # 应用掩码（如果提供）
        if attn_mask is not None:
            scores = scores.masked_fill(attn_mask == 0, -1e9)

        # Softmax with numerical stability
        attn_weights = F.softmax(scores, dim=-1)

        # attn_weights = self.dropout(attn_weights)  ## if dropout uncomment this line!

        # 应用注意力权重
        attn_output = torch.matmul(attn_weights, V)

        # 重塑回原始格式
        attn_output = attn_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )

        # 最终线性投影
        output = self.out_linear(attn_output)

        return output, attn_weights.mean(dim=1)  # 返回平均注意力权重


class TokenLearner(nn.Module):
    """
    Attention-based token learning layer that reduces sequence length.

    Converts from TensorFlow TokenLearner to PyTorch implementation.
    Reduces sequence length to a fixed number of learned tokens.
    """

    def __init__(self, num_tokens, channels):
        """
        Initialize TokenLearner.

        Args:
            num_tokens: Number of output tokens to learn
            channels: Number of input channels
        """
        super(TokenLearner, self).__init__()
        self.num_tokens = num_tokens
        self.channels = channels

        # Learnable attention weights for each token
        # Will be initialized on first forward pass to handle dynamic sequence lengths
        self.attention = None

    def forward(self, x):
        """
        Forward pass through TokenLearner.

        Args:
            x: Input tensor of shape (batch_size, seq_len, channels)
        Returns:
            Learned tokens of shape (batch_size, num_tokens, channels)
        """
        batch_size, seq_len, channels = x.shape

        # Initialize layers on first forward pass if not already initialized
        if self.attention is None:
            self.attention = nn.Linear(channels, self.num_tokens).to(x.device)
            # Initialize with small weights for stable training
            nn.init.xavier_uniform_(self.attention.weight, gain=0.1)
            if self.attention.bias is not None:
                nn.init.constant_(self.attention.bias, 0.0)

        # Compute attention weights: (batch_size, seq_len, num_tokens)
        attention_weights = self.attention(x)
        attention_weights = F.softmax(attention_weights, dim=1)

        # Apply attention to get learned tokens: (batch_size, num_tokens, channels)
        # attention_weights: (batch_size, seq_len, num_tokens)
        # x: (batch_size, seq_len, channels)
        # output: (batch_size, num_tokens, channels)
        learned_tokens = torch.matmul(attention_weights.transpose(1, 2), x)

        return learned_tokens


class SEBlock(nn.Module):
    """
    Squeeze-and-Excitation block for channel attention.

    Converts from TensorFlow SEBlock to PyTorch implementation.
    Applies channel-wise attention to enhance important features.
    """

    def __init__(self, channels, reduction=16):
        """
        Initialize SEBlock.

        Args:
            channels: Number of input channels
            reduction: Reduction ratio for the bottleneck layer
        """
        super(SEBlock, self).__init__()
        self.channels = channels
        self.reduction = reduction

        # Squeeze: Global average pooling
        self.global_avg_pool = nn.AdaptiveAvgPool1d(1)

        # Excitation: Two fully connected layers
        self.fc1 = nn.Linear(channels, channels // reduction)
        self.fc2 = nn.Linear(channels // reduction, channels)

        # Initialize weights
        self._init_weights()

    def _init_weights(self):
        """Initialize weights for stable training."""
        nn.init.xavier_uniform_(self.fc1.weight, gain=0.1)
        nn.init.xavier_uniform_(self.fc2.weight, gain=0.1)
        if self.fc1.bias is not None:
            nn.init.constant_(self.fc1.bias, 0.0)
        if self.fc2.bias is not None:
            nn.init.constant_(self.fc2.bias, 0.0)

    def forward(self, x):
        """
        Forward pass through SEBlock.

        Args:
            x: Input tensor of shape (batch_size, seq_len, channels)
        Returns:
            Output tensor of shape (batch_size, seq_len, channels)
        """
        batch_size, seq_len, channels = x.shape

        # Squeeze: Global average pooling over sequence dimension
        # Transpose to (batch_size, channels, seq_len) for pooling
        squeezed = self.global_avg_pool(x.transpose(1, 2))  # (batch_size, channels, 1)
        squeezed = squeezed.view(batch_size, channels)  # (batch_size, channels)

        # Excitation: Two FC layers with ReLU and Sigmoid
        excited = F.relu(self.fc1(squeezed))  # (batch_size, channels // reduction)
        excited = torch.sigmoid(self.fc2(excited))  # (batch_size, channels)

        # Scale: Apply channel-wise scaling
        excited = excited.view(batch_size, 1, channels)  # (batch_size, 1, channels)
        scaled = x * excited  # Broadcasting: (batch_size, seq_len, channels)

        return scaled


class LearnablePositionalEncoding(nn.Module):
    """
    Learnable positional encoding layer.

    Converts from TensorFlow LearnablePositionalEncoding to PyTorch implementation.
    Adds trainable positional embeddings to input sequences.
    """

    def __init__(self, max_seq_len, d_model):
        """
        Initialize LearnablePositionalEncoding.

        Args:
            max_seq_len: Maximum sequence length
            d_model: Model dimension
        """
        super(LearnablePositionalEncoding, self).__init__()
        self.max_seq_len = max_seq_len
        self.d_model = d_model

        # Learnable positional embeddings
        self.pos_embedding = nn.Parameter(torch.randn(1, max_seq_len, d_model))

        # Initialize with small values for stable training
        nn.init.normal_(self.pos_embedding, mean=0.0, std=0.02)

    def forward(self, x):
        """
        Forward pass through LearnablePositionalEncoding.

        Args:
            x: Input tensor of shape (batch_size, seq_len, d_model)
        Returns:
            Output tensor of shape (batch_size, seq_len, d_model)
        """
        batch_size, seq_len, d_model = x.shape

        # Ensure sequence length doesn't exceed maximum
        if seq_len > self.max_seq_len:
            raise ValueError(f"Sequence length {seq_len} exceeds maximum {self.max_seq_len}")

        # Add positional encoding
        pos_enc = self.pos_embedding[:, :seq_len, :]  # (1, seq_len, d_model)
        return x + pos_enc


class GlobalAveragePooling(nn.Module):
    """
    Global Average Pooling layer.

    Equivalent to TensorFlow's GlobalAveragePooling1D.
    Computes the average of each feature across the sequence dimension.
    """

    def __init__(self):
        """Initialize GlobalAveragePooling."""
        super(GlobalAveragePooling, self).__init__()

    def forward(self, x):
        """
        Forward pass through GlobalAveragePooling.

        Args:
            x: Input tensor of shape (batch_size, seq_len, features)

        Returns:
            Pooled tensor of shape (batch_size, features)
        """
        if len(x.shape) == 3:
            # x is (batch_size, seq_len, features)
            # Average over sequence dimension (dim=1)
            return torch.mean(x, dim=1)  # (batch_size, features)
        else:
            raise ValueError(f"Expected 3D input tensor, got {len(x.shape)}D tensor")





class ConvolutionalNet(nn.Module):
    """
    Standard Convolutional Neural Network for sequence data.

    This CNN implementation provides convolutional feature extraction
    for the intrusion detection model. It uses standard 1D convolutions with
    batch normalization and residual connections.

    Architecture:
    - Multiple 1D convolutional layers with increasing channels
    - Batch normalization for stable training
    - ReLU activations for non-linearity
    - Residual connections to prevent degradation
    - Adaptive pooling to handle variable sequence lengths
    """

    def __init__(self, num_inputs, num_channels, kernel_size=3, dropout=0.2):
        """
        Initialize the CNN with configurable architecture.

        Args:
            num_inputs: Number of input features
            num_channels: List of channel sizes for each CNN layer
            kernel_size: Kernel size for convolutions (default: 3)
            dropout: Dropout rate for regularization
        """
        super(ConvolutionalNet, self).__init__()

        layers = []
        num_levels = len(num_channels)

        for i in range(num_levels):
            in_channels = num_inputs if i == 0 else num_channels[i-1]
            out_channels = num_channels[i]

            # Convolutional block
            layers.append(ConvBlock(
                in_channels=in_channels,
                out_channels=out_channels,
                kernel_size=kernel_size,
                dropout=dropout
            ))

        self.network = nn.Sequential(*layers)

    def forward(self, x):
        """
        Forward pass through the CNN.

        Args:
            x: Input tensor of shape (batch_size, seq_len, num_inputs)
        Returns:
            Output tensor of shape (batch_size, seq_len, num_channels[-1])
        """
        # CNN expects (batch_size, features, seq_len) format for Conv1d
        x = x.transpose(1, 2)  # (batch_size, features, seq_len)
        x = self.network(x)    # Apply CNN layers
        x = x.transpose(1, 2)  # Back to (batch_size, seq_len, features)
        return x

class ConvBlock(nn.Module):
    """
    Convolutional block with batch normalization and residual connections.

    Architecture: Conv1d -> BatchNorm -> ReLU -> Dropout -> Residual Connection
    """

    def __init__(self, in_channels, out_channels, kernel_size=3, dropout=0.2):
        """
        Initialize the convolutional block.

        Args:
            in_channels: Number of input channels
            out_channels: Number of output channels
            kernel_size: Kernel size for convolution
            dropout: Dropout rate
        """
        super(ConvBlock, self).__init__()

        # Calculate padding to maintain sequence length
        padding = (kernel_size - 1) // 2

        # Main convolutional path
        self.conv = nn.Conv1d(in_channels, out_channels, kernel_size, padding=padding)
        self.batch_norm = nn.BatchNorm1d(out_channels)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout)

        # Residual connection (if dimensions differ)
        self.residual = nn.Conv1d(in_channels, out_channels, 1) if in_channels != out_channels else None

        # Initialize weights
        self.init_weights()

    def init_weights(self):
        """Initialize weights for stable training."""
        nn.init.kaiming_normal_(self.conv.weight, mode='fan_out', nonlinearity='relu')
        if self.conv.bias is not None:
            nn.init.constant_(self.conv.bias, 0)

        if self.residual is not None:
            nn.init.kaiming_normal_(self.residual.weight, mode='fan_out', nonlinearity='relu')
            if self.residual.bias is not None:
                nn.init.constant_(self.residual.bias, 0)

    def forward(self, x):
        """
        Forward pass through the convolutional block.

        Args:
            x: Input tensor of shape (batch_size, in_channels, seq_len)
        Returns:
            Output tensor of shape (batch_size, out_channels, seq_len)
        """
        # Main path
        out = self.relu(self.conv(x))
        out = self.batch_norm(out)
        out = self.dropout(out)

        # Residual connection
        residual = x if self.residual is None else self.residual(x)

        return out + residual




    
class TransformerEncoder(nn.Module):
    """
    Transformer Encoder for sequence modeling in intrusion detection.

    Transformers excel at capturing long-range dependencies and global patterns
    through self-attention mechanisms. Key advantages for intrusion detection:
    - Parallel processing of entire sequences
    - Attention weights provide interpretability
    - Effective at modeling complex relationships between network features
    - Handles variable-length sequences naturally
    """
    def __init__(self, input_dim, d_model, nhead, num_layers, dim_feedforward=256, dropout=0.1):
        """
        Initialize Transformer encoder with multi-head self-attention.

        Args:
            input_dim: Input feature dimension (e.g., 11 for CarH, 78 for CICIDS2017)
            d_model: Model dimension (must be divisible by nhead)
            nhead: Number of attention heads (e.g., 8, 16)
            num_layers: Number of transformer encoder layers (e.g., 2, 3)
            dim_feedforward: Feedforward network dimension (default: 2048)
            dropout: Dropout rate for regularization (default: 0.1)
        """
        super(TransformerEncoder, self).__init__()

        self.d_model = d_model

        # Project input features to model dimension
        self.input_projection = nn.Linear(input_dim, d_model)

        # Create transformer encoder layer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=nhead,
            dim_feedforward=dim_feedforward,
            dropout=dropout,
            batch_first=False  # Use (seq_len, batch_size, d_model) format
        )

        # Stack multiple encoder layers
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers)
        #self.dropout = nn.Dropout(dropout)  ## if dropout uncomment this line!

    def forward(self, x):
        """
        Forward pass through Transformer encoder.

        Args:
            x: Input tensor of shape (batch_size, seq_len, input_dim)
               e.g., (32, 5, 78) for CICIDS2017 with sequence length 5
        Returns:
            Output tensor of shape (batch_size, seq_len, d_model)
            e.g., (32, 5, 128) if d_model = 128
        """
        # Project input features to model dimension
        x = self.input_projection(x)  # (batch_size, seq_len, d_model)

        # Transpose for transformer: (seq_len, batch_size, d_model)
        x = x.transpose(0, 1)

        # Apply dropout directly without positional encoding
        #x = self.dropout(x)  ## if dropout uncomment this line!

        # Apply transformer encoder layers
        x = self.transformer_encoder(x)

        # Transpose back to (batch_size, seq_len, d_model)
        x = x.transpose(0, 1)

        return x

class IntrusionDetectionModel(nn.Module):
    """
    Light CNN + Transformer fusion model for network intrusion detection.

    This model combines dual paths with bi-directional cross-attention:
    1. CNN Path: Light convolutional layers with SE attention
    2. Transformer Path: Dense projection with learnable positional encoding
    3. TokenLearner: Reduces sequence length to 4 tokens in both paths
    4. Bi-directional Cross-Attention: CNN and Transformer features interact
    5. Global pooling and classification

    Architecture Overview:
    Input (N, T, f) -> [CNN Path] -> TokenLearner -> (N, 4, 32)
                    -> [Transformer Path] -> TokenLearner -> (N, 4, 32)
    -> Bi-directional Cross-Attention -> Global Pooling -> Classification

    Supports variable sequence lengths and multiple intrusion detection datasets.
    """
    def __init__(self, input_dim, num_classes, seq_len=1, d_model=128, nhead=1,
                 dropout=0.3, hidden_dim=64, cnn_channels=32):
        """
        Initialize the light CNN + Transformer fusion model.

        Args:
            input_dim: Number of input features (11 for CarH, 78 for CICIDS2017, 42 for ToNIoT)
            num_classes: Number of output classes (5 for CarH, 8 for CICIDS2017, 10 for ToNIoT)
            seq_len: Sequence length (T dimension, typically 1-50)
            d_model: Model dimension for both CNN and Transformer paths (default: 128)
            nhead: Number of attention heads for cross-attention (default: 1)
            dropout: Dropout rate for regularization (default: 0.3)
            hidden_dim: Hidden dimension for final classifier (default: 64)
            cnn_channels: Number of CNN channels for CNN path (default: 32)
        """
        super(IntrusionDetectionModel, self).__init__()

        # Store model configuration
        self.input_dim = input_dim
        self.num_classes = num_classes
        self.seq_len = seq_len
        self.d_model = d_model
        self.nhead = nhead
        self.dropout = dropout
        self.hidden_dim = hidden_dim
        self.cnn_channels = cnn_channels

        # --- CNN Path ---
        self.cnn_conv = nn.Conv1d(input_dim, cnn_channels, kernel_size=3, padding=1)
        self.cnn_bn = nn.BatchNorm1d(cnn_channels)
        self.cnn_se = SEBlock(cnn_channels, reduction=8)
        self.cnn_token_learner = TokenLearner(num_tokens=4, channels=cnn_channels)

        # --- Transformer Path ---
        self.trans_projection = nn.Linear(input_dim, d_model)
        self.trans_pos_encoding = LearnablePositionalEncoding(seq_len, d_model)
        self.trans_layer_norm = nn.LayerNorm(d_model)
        self.trans_token_learner = TokenLearner(num_tokens=4, channels=d_model)

        # --- Bi-directional Cross-Attention ---
        # Both attention modules use the same dimension for compatibility
        self.cross_attn1 = CustomMultiheadAttention(d_model=cnn_channels, num_heads=nhead, dropout=dropout)
        self.cross_attn2 = CustomMultiheadAttention(d_model=cnn_channels, num_heads=nhead, dropout=dropout)

        # Projection layers to align dimensions
        self.cnn_proj_to_channels = nn.Linear(cnn_channels, cnn_channels)  # Identity projection for CNN features
        self.trans_proj_to_channels = nn.Linear(d_model, cnn_channels)  # Project transformer features to cnn_channels

        # --- Final Classification ---
        self.global_pool = GlobalAveragePooling()
        self.classifier = nn.Sequential(
            nn.Linear(cnn_channels * 2, hidden_dim),  # cnn_channels*2 from concatenated features
            # nn.Dropout(dropout),  ## if dropout uncomment this line!
            nn.Linear(hidden_dim, num_classes)
        )

    def _initialize_weights(self):
        """
        Initialize model weights using appropriate initialization strategies.

        - Linear layers: Xavier normal initialization for stable gradients
        - BatchNorm layers: Weight=1, Bias=0 (standard initialization)
        - TCN and Transformer components have their own initialization
        """
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_normal_(m.weight)  # Good for ReLU activations
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm1d):
                nn.init.constant_(m.weight, 1)  # Scale parameter
                nn.init.constant_(m.bias, 0)   # Shift parameter

    def forward(self, x):
        """
        Forward pass through the light CNN + Transformer fusion model.

        Processing Pipeline:
        1. Input shape handling
        2. Dual path processing (CNN + Transformer)
        3. TokenLearner for sequence reduction
        4. Bi-directional cross-attention
        5. Global pooling and classification

        Args:
            x: Input tensor with flexible shapes:
               - (batch_size, input_dim): Single time step
               - (batch_size, seq_len, input_dim): Sequence data
        Returns:
            Output tensor of shape (batch_size, num_classes)
            Contains logits for each class
        """
        # Handle different input shapes for flexibility
        if len(x.shape) == 2:
            # Single time step: (batch_size, input_dim) -> (batch_size, 1, input_dim)
            x = x.unsqueeze(1)
        elif len(x.shape) > 3:
            # Flatten extra dimensions to ensure (batch_size, T, f) format
            x = x.view(x.size(0), -1, x.size(-1))

        # --- CNN Path ---
        # x shape: (batch_size, seq_len, input_dim)
        # Transpose for Conv1d: (batch_size, input_dim, seq_len)
        cnn_input = x.transpose(1, 2)
        cnn1 = F.relu(self.cnn_conv(cnn_input))  # (batch_size, 32, seq_len)
        # print('==========================================', cnn1 , cnn1.size())
        cnn2 = self.cnn_bn(cnn1)
        # Transpose back for SE and TokenLearner: (batch_size, seq_len, 32)
        cnn3 = cnn2.transpose(1, 2)
        cnn4 = self.cnn_se(cnn3)  # (batch_size, seq_len, 32)
        cnn_feat = self.cnn_token_learner(cnn4)  # (batch_size, 4, 32)



        # --- Transformer Path ---
        trans = self.trans_projection(x)  # (batch_size, seq_len, d_model)
        trans1 = self.trans_pos_encoding(trans)  # (batch_size, seq_len, d_model)
        trans2 = self.trans_layer_norm(trans1)  # (batch_size, seq_len, d_model)
        trans_feat = self.trans_token_learner(trans2)  # (batch_size, 4, d_model)

        # --- Bi-directional Cross-Attention ---
        # Project features to common dimension (cnn_channels) for cross-attention
        cnn_feat_proj = self.cnn_proj_to_channels(cnn_feat)  # (batch_size, 4, cnn_channels)
        trans_feat_proj = self.trans_proj_to_channels(trans_feat)  # (batch_size, 4, cnn_channels)

        # CNN features attend to Transformer features
        attn1, _ = self.cross_attn1(cnn_feat_proj, trans_feat_proj, trans_feat_proj)  # (batch_size, 4, cnn_channels)
        # Transformer features attend to CNN features
        attn2, _ = self.cross_attn2(trans_feat_proj, cnn_feat_proj, cnn_feat_proj)  # (batch_size, 4, cnn_channels)

        # Concatenate attention outputs (both are now cnn_channels-dimensional)
        merged = torch.cat([attn1, attn2], dim=-1)  # (batch_size, 4, cnn_channels*2)

        # --- Global Pooling + Classification ---
        # Global average pooling: (batch_size, 64)
        pooled = self.global_pool(merged)  # GlobalAveragePooling handles dimensions automatically

        # Final classification
        output = self.classifier(pooled)  # (batch_size, num_classes)

        return output

# Convenience functions for creating models for different datasets

def create_intrusion_detection_model(dataset_name, input_dim, seq_len=1,
                                    cnn_layers=2, transformer_layers=2,
                                    d_model=128, nhead=8, dropout=0.1, hidden_dim=128, cnn_channels=64):
    """
    Factory function to create optimized CNN + Transformer fusion models for specific datasets.

    This function automatically configures model architecture parameters based on
    dataset characteristics and complexity. It provides sensible defaults while
    allowing customization of key hyperparameters.

    Dataset Characteristics:
    - CarH: Small dataset (11 features, 5 classes) - lightweight model
    - CICIDS2017: Large dataset (78 features, 8 classes) - complex model
    - ToNIoT: Medium dataset (42 features, 10 classes) - balanced model

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features (should match dataset expectations)
        seq_len: Sequence length for temporal modeling (1 for single time step)
        cnn_layers: Number of CNN layers (2-4 recommended)
        transformer_layers: Number of Transformer layers (1-3 recommended)
        d_model: Transformer model dimension (auto-adjusted based on dataset)
        nhead: Number of attention heads (must divide d_model evenly)
        dropout: Dropout rate for regularization (0.1-0.3)
        hidden_dim: Hidden dimension for classifier (auto-adjusted based on dataset)
        cnn_channels: Base number of CNN channels (default: 64). Will be used to generate channel list.

    Returns:
        IntrusionDetectionModel: Configured model instance ready for training

    Raises:
        ValueError: If dataset_name is not supported
    """
    # Dataset-specific configurations with expected features and classes
    dataset_configs = {
        'carh': {'num_classes': 5, 'input_features': 11},
        'cicids2017': {'num_classes': 8, 'input_features': 78},
        'toniot': {'num_classes': 10, 'input_features': 42}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}. Supported: {list(dataset_configs.keys())}")

    config = dataset_configs[dataset_name]
    num_classes = config['num_classes']

    # Validate input_dim matches expected features for the dataset
    expected_features = config['input_features']
    if input_dim != expected_features:
        print(f"Warning: input_dim ({input_dim}) doesn't match expected features for {dataset_name} ({expected_features})")

    # 如果 cnn_channels 是整数，转换为列表；如果已经是列表，保持不变
    if isinstance(cnn_channels, int):
        cnn_channels_list = [cnn_channels, cnn_channels * 2][:cnn_layers]
    else:
        cnn_channels_list = [32, 64][:cnn_layers]

    # Create and return the configured model
    model = IntrusionDetectionModel(
        input_dim=input_dim,
        num_classes=num_classes,
        seq_len=seq_len,
        cnn_layers=cnn_layers,
        transformer_layers=transformer_layers,
        cnn_channels=cnn_channels_list,
        d_model=d_model,
        nhead=nhead,
        dropout=dropout,
        hidden_dim=hidden_dim
    )

    return model


class TransformerOnlyModel(nn.Module):
    """
    Pure Transformer model for network intrusion detection.

    This model uses only Transformer architecture without convolution layers,
    focusing purely on self-attention mechanisms to capture global patterns
    and long-range dependencies in network traffic data.

    Architecture:
    Input (N, T, f) -> Input Projection -> Transformer Encoder -> Global Pooling -> Classification
    """

    def __init__(self, input_dim, num_classes, seq_len=1, transformer_layers=4,
                 d_model=256, nhead=8, dropout=0.1, hidden_dim=512):
        """
        Initialize pure Transformer model for intrusion detection.

        Args:
            input_dim: Number of input features (e.g., 78 for CICIDS2017)
            num_classes: Number of output classes (e.g., 8 for CICIDS2017)
            seq_len: Sequence length for temporal modeling (default: 1)
            transformer_layers: Number of transformer encoder layers (default: 4)
            d_model: Model dimension for transformer (default: 256)
            nhead: Number of attention heads (default: 8)
            dropout: Dropout rate for regularization (default: 0.1)
            hidden_dim: Hidden dimension for final classifier (default: 512)
        """
        super(TransformerOnlyModel, self).__init__()

        # Store model configuration
        self.input_dim = input_dim
        self.num_classes = num_classes
        self.seq_len = seq_len
        self.transformer_layers = transformer_layers
        self.d_model = d_model
        self.nhead = nhead
        self.dropout = dropout
        self.hidden_dim = hidden_dim

        # Input projection to transformer dimension
        self.input_projection = nn.Linear(input_dim, d_model)


        # Create custom transformer encoder layers directly
        self.transformer_layers = nn.ModuleList()
        for i in range(transformer_layers):
            # 自注意力层
            self_attn = CustomMultiheadAttention(d_model, nhead, dropout)

            # 前馈网络
            linear1 = nn.Linear(d_model, 256)
            linear2 = nn.Linear(256, d_model)

            # 层归一化
            norm1 = nn.LayerNorm(d_model, eps=1e-6)
            norm2 = nn.LayerNorm(d_model, eps=1e-6)

            # Dropout
            # dropout1 = nn.Dropout(dropout)  ## if dropout uncomment this line!
            # dropout2 = nn.Dropout(dropout)  ## if dropout uncomment this line!
            # dropout_ff = nn.Dropout(dropout)  ## if dropout uncomment this line!


            # 将层组件存储为字典
            layer_dict = nn.ModuleDict({
                'self_attn': self_attn,
                'linear1': linear1,
                'linear2': linear2,
                'norm1': norm1,
                'norm2': norm2,
                # 'dropout1': dropout1,  ## if dropout uncomment this line!
                # 'dropout2': dropout2,  ## if dropout uncomment this line!
                # 'dropout_ff': dropout_ff  ## if dropout uncomment this line!
            })

            self.transformer_layers.append(layer_dict)

        # Global pooling to convert sequences to fixed-size representations
        self.global_avg_pool = GlobalAveragePooling()

        # Final classifier
        self.classifier = nn.Sequential(
            nn.Linear(d_model, hidden_dim),
            # nn.Dropout(dropout),  ## if dropout uncomment this line!
            nn.Linear(hidden_dim, num_classes)
        )

    def forward(self, x):
        """
        Forward pass through pure Transformer model.

        Args:
            x: Input tensor with flexible shapes:
               - (batch_size, input_dim): Single time step
               - (batch_size, seq_len, input_dim): Sequence data
        Returns:
            Output tensor of shape (batch_size, num_classes)
            Contains logits for each class
        """
        # Handle different input shapes
        if len(x.shape) == 2:
            # Single time step: (batch_size, input_dim) -> (batch_size, 1, input_dim)
            x = x.unsqueeze(1)
        elif len(x.shape) > 3:
            # Flatten extra dimensions to ensure (batch_size, T, f) format
            x = x.view(x.size(0), -1, x.size(-1))


        # Project input features to transformer dimension
        x_projected = self.input_projection(x)  # (batch_size, seq_len, d_model)

        # print('=======================x_projected', x_projected, x_projected.size())

        # Transformer processing - apply custom transformer encoder layers
        transformer_out = x_projected
        for i, layer_dict in enumerate(self.transformer_layers):
            # print(f"🔍 处理第 {i} 层...")

            # Self-attention block
            src2, _ = layer_dict['self_attn'](
                transformer_out, transformer_out, transformer_out
            )
            # src2 = layer_dict['dropout1'](src2)  ## if dropout uncomment this line!
            transformer_out = transformer_out + src2  # 残差连接
            transformer_out = layer_dict['norm1'](transformer_out)

            # Feed forward block
            src2 = layer_dict['linear1'](transformer_out)
            src2 = F.relu(src2)
            # src2 = layer_dict['dropout_ff'](src2)  ## if dropout uncomment this line!
            src2 = layer_dict['linear2'](src2)

            # src2 = layer_dict['dropout2'](src2)  ## if dropout uncomment this line!
            transformer_out = transformer_out + src2  # 残差连接
            transformer_out = layer_dict['norm2'](transformer_out)

        # transformer_out shape: (batch_size, seq_len, d_model)
        # print('=======================transformer_out',  transformer_out.size(), transformer_out)


        # print('=======================transformer_out', transformer_out)
        # Global pooling: Convert sequences to fixed-size vectors
        pooled_out = self.global_avg_pool(transformer_out)  # (batch_size, d_model)
        # print('=======================pooled_out', pooled_out)

        # Classification
        output = self.classifier(pooled_out)  # (batch_size, num_classes)

        return output


def create_transformer_only_model(dataset_name, input_dim, seq_len=1, transformer_layers=4,
                                 d_model=256, nhead=8, dropout=0.1, hidden_dim=512):
    """
    Factory function to create pure Transformer models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length for temporal modeling
        transformer_layers: Number of transformer encoder layers
        d_model: Model dimension for transformer
        nhead: Number of attention heads
        dropout: Dropout rate for regularization
        hidden_dim: Hidden dimension for final classifier

    Returns:
        TransformerOnlyModel instance configured for the dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    # Create and return the configured model
    model = TransformerOnlyModel(
        input_dim=input_dim,
        num_classes=num_classes,
        seq_len=seq_len,
        transformer_layers=transformer_layers,
        d_model=d_model,
        nhead=nhead,
        dropout=dropout,
        hidden_dim=hidden_dim
    )

    return model


class CNNOnlyModel(nn.Module):
    """
    Pure CNN model for network intrusion detection (pruning-enabled version).

    This model uses only CNN architecture without transformer layers,
    focusing purely on convolutional operations to capture local patterns
    and temporal features in network traffic data.

    Architecture:
    Input (N, f) -> Conv1D → ReLU → MaxPooling1D -> Classification
    """

    def __init__(self, input_dim, num_classes):
        """
        Initialize pure CNN model for intrusion detection (EdgeCNN style).

        Args:
            input_dim: Number of input features (e.g., 78 for CICIDS2017)
            num_classes: Number of output classes (e.g., 8 for CICIDS2017)
        """
        super(CNNOnlyModel, self).__init__()

        # Store model configuration
        self.input_dim = input_dim
        self.num_classes = num_classes

        # CNN layers using torch.nn.Sequential (following EdgeCNN style)
        # Using Conv1d and MaxPool1d with fixed channels [32, 64]
        self.conv1 = torch.nn.Sequential(torch.nn.Conv1d(1, 32, kernel_size=5, stride=1, padding=2),
                                         torch.nn.ReLU(),
                                         torch.nn.MaxPool1d(kernel_size=2),
                                         torch.nn.Conv1d(32, 64, kernel_size=5, stride=1, padding=2),
                                         torch.nn.ReLU(),
                                         torch.nn.MaxPool1d(kernel_size=2)
                                         )

        # Calculate output dimension after two MaxPool1d operations
        # Use actual MaxPool1d formula: floor((input_size - kernel_size) / stride) + 1
        # For kernel_size=2, stride=2 (default)
        dim_after_pool1 = ((input_dim - 2) // 2) + 1  # First MaxPool1d
        dim_after_pool2 = ((dim_after_pool1 - 2) // 2) + 1  # Second MaxPool1d

        # Store for dynamic calculation in forward
        self.expected_features = dim_after_pool2 * 64

        # Dense layers using torch.nn.Sequential (following EdgeCNN style)
        # Use a more conservative approach - will be set dynamically in first forward pass
        self.dense = None
        self.num_classes = num_classes

        self.softmax = nn.Softmax(dim=1)

    def forward(self, x):
        """
        Forward pass through pure CNN model (EdgeCNN style).

        Args:
            x: Input tensor of shape (batch_size, input_dim)
        Returns:
            Output tensor of shape (batch_size, num_classes)
            Contains logits for each class
        """
        # Handle input shape: (batch_size, input_dim) -> (batch_size, 1, input_dim)
        # Following EdgeCNN pattern but using actual data dimensions
        if len(x.shape) == 2:
            x = x.unsqueeze(1)  # Add channel dimension: (batch_size, 1, input_dim)

        # CNN processing following EdgeCNN style
        x4 = self.conv1(x)  # Conv1d processing: (batch_size, 64, actual_pooled_dim)

        # Dynamic flattening - use actual tensor dimensions (fix the batch_size bug)
        batch_size = x4.size(0)
        flattened_size = x4.size(1) * x4.size(2)  # channels * pooled_length
        x5 = x4.view(batch_size, flattened_size)  # Flatten output

        # Initialize dense layers on first forward pass (dynamic sizing)
        if self.dense is None:
            self.dense = torch.nn.Sequential(
                torch.nn.Linear(flattened_size, 128),
                # torch.nn.Dropout(0.2),  ## if dropout uncomment this line!
                torch.nn.Linear(128, self.num_classes)
            ).to(x.device)  # Ensure same device as input

            # Apply weight initialization to dynamically created layers
            from torch.nn import init
            for layer in self.dense:
                if isinstance(layer, torch.nn.Linear):
                    init.xavier_normal_(layer.weight.data)
                    if layer.bias is not None:
                        init.constant_(layer.bias.data, 0)

        # Dense layers
        output = self.dense(x5)

        return output  # Return logits (no softmax for training)


def create_cnn_only_model(dataset_name, input_dim, seq_len=1, cnn_layers=2,
                         cnn_channels=None, hidden_dim=128):
    """
    Factory function to create pure CNN models for specific datasets.

    Args:
        dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
        input_dim: Number of input features
        seq_len: Sequence length for temporal modeling
        cnn_layers: Number of CNN layers
        cnn_channels: List of channel sizes for CNN layers
        hidden_dim: Hidden dimension for final classifier

    Returns:
        CNNOnlyModel instance configured for the dataset
    """
    # Dataset-specific configurations
    dataset_configs = {
        'carh': {'classes': 5},
        'cicids2017': {'classes': 8},
        'toniot': {'classes': 10}
    }

    if dataset_name not in dataset_configs:
        raise ValueError(f"Unsupported dataset: {dataset_name}")

    config = dataset_configs[dataset_name]
    num_classes = config['classes']

    # Default CNN channel configuration
    if cnn_channels is None:
        cnn_channels = [64, 128][:cnn_layers]

    # Create and return the configured model (EdgeCNN style with fixed parameters)
    model = CNNOnlyModel(
        input_dim=input_dim,
        num_classes=num_classes
    )

    return model


def weight_init(m):
    """
    Initialize weights for different layer types with safe initialization.

    Args:
        m: PyTorch module to initialize
    """

    return
    # if isinstance(m, nn.Linear):
    #     init.xavier_normal_(m.weight.data)
    #     init.normal_(m.bias.data)
    # elif isinstance(m, nn.Conv1d):
    #     init.normal_(m.weight.data)
    #     if m.bias is not None:
    #         init.normal_(m.bias.data)
    # elif isinstance(m, nn.BatchNorm1d):
    #     init.normal_(m.weight.data, 1.0, 0.02)
    #     init.constant_(m.bias.data, 0.0)
    # elif isinstance(m, nn.LayerNorm):
    #     init.constant_(m.bias.data, 0.0)
    #     init.constant_(m.weight.data, 1.0)
    # elif isinstance(m, nn.Embedding):
    #     init.normal_(m.weight.data, 1.0, 0.02)
    # elif isinstance(m, nn.LSTM):
    #     for name, param in m.named_parameters():
    #         if 'weight' in name:
    #             init.xavier_normal_(param.data)
    #         elif 'bias' in name:
    #             init.normal_(param.data)
    # elif isinstance(m, nn.GRU):
    #     for name, param in m.named_parameters():
    #         if 'weight' in name:
    #             init.xavier_normal_(param.data)
    #         elif 'bias' in name:
    #             init.normal_(param.data)
    # return



# Legacy models for backward compatibility (if needed)
class LeNet5Mnist(nn.Module):
    """Legacy LeNet5 for MNIST - kept for compatibility."""
    def __init__(self):
        super(LeNet5Mnist, self).__init__()
        self.conv1 = nn.Conv2d(1, 10, kernel_size=5)
        self.conv2 = nn.Conv2d(10, 20, kernel_size=5)
        # self.conv2_drop = nn.Dropout2d()  ## The original version in LG-FedAvg has the dropout,
        # but in our setup since we are doing pruning, we removed it
        self.fc1 = nn.Linear(320, 50)
        self.fc2 = nn.Linear(50, 10)

    def forward(self, x):
        x = F.relu(F.max_pool2d(self.conv1(x), 2))
        #x = F.relu(F.max_pool2d(self.conv2_drop(self.conv2(x)), 2))   ## if dropout uncomment this line!
        x = F.relu(F.max_pool2d(self.conv2(x), 2))
        x = x.view(-1, x.shape[1]*x.shape[2]*x.shape[3])
        x = F.relu(self.fc1(x))
        #x = F.dropout(x, training=self.training)   ## if dropout uncomment this line!
        x = self.fc2(x)
        return x

class LeNet5Cifar10(nn.Module):
    """Legacy LeNet5 for CIFAR10 - kept for compatibility."""
    def __init__(self):
        super(LeNet5Cifar10, self).__init__()
        self.conv1 = nn.Conv2d(3, 6, 5)
        #self.bn1 = nn.BatchNorm2d(6)  ## if batch norm uncomment this line!
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)
        #self.bn2 = nn.BatchNorm2d(16)  ## if batch norm uncomment this line!
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 10)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

class LeNet5Cifar100(nn.Module):
    """Legacy LeNet5 for CIFAR100 - kept for compatibility."""
    def __init__(self):
        super(LeNet5Cifar100, self).__init__()
        self.conv1 = nn.Conv2d(3, 6, 5)
        #self.bn1 = nn.BatchNorm2d(6)  ## if batch norm uncomment this line!
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(6, 16, 5)
        #self.bn2 = nn.BatchNorm2d(16)  ## if batch norm uncomment this line!
        self.fc1 = nn.Linear(16 * 5 * 5, 120)
        self.fc2 = nn.Linear(120, 84)
        self.fc3 = nn.Linear(84, 100)

    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 16 * 5 * 5)
        x = F.relu(self.fc1(x))
        x = F.relu(self.fc2(x))
        x = self.fc3(x)
        return x

# # GRU and LSTM models for intrusion detection (pruning-enabled versions)

# class GRU(nn.Module):
#     def __init__(self, dim_in, dim_out, lstm_num_layers=1, dim_hidden=64):
#         super(GRU, self).__init__()

#         self.hidden_dim = dim_hidden
#         self.num_layers = lstm_num_layers

#         # gru
#         self.bigru = nn.GRU(input_size=dim_in, hidden_size=dim_hidden, num_layers=self.num_layers, bidirectional=False, batch_first=True)
#         self.linear = nn.Linear(dim_hidden, dim_out)

#     def forward(self, x):
#         batch_size, seq_len = x.size(0), x.size(1)
#         x1, _ = self.bigru(x)
#         outputs = self.linear(x1.contiguous().view(batch_size * seq_len, self.hidden_dim))
#         return outputs

# class LSTM(nn.Module):
#     def __init__(self, dim_in, dim_out, seq, dim_hidden=128):
#         super(LSTM, self).__init__()

#         self.lstm_hidden_dim = dim_hidden
#         self.seq = seq
#         self.linear1 = nn.Linear(in_features=dim_in, out_features=dim_hidden)
#         self.linear2 = nn.Linear(in_features=dim_hidden, out_features=dim_hidden)
#         self.lstm = nn.LSTM(input_size=dim_hidden, hidden_size=dim_hidden, num_layers=1, bidirectional=False,
#                             batch_first=True)
#         self.lstm1 = nn.LSTM(input_size=dim_hidden, hidden_size=dim_hidden*2, num_layers=1, bidirectional=False,
#                             batch_first=True)
#         self.linear3 = nn.Linear(in_features=seq*dim_hidden*2, out_features=dim_out)

#     def forward(self, x):
#         batch_size = x.size(0)
#         x2 = self.linear1(x)
#         x2 = self.linear2(x2)
#         recurrent_features, _ = self.lstm(x2)
#         recurrent_features1, _ = self.lstm1(recurrent_features)
#         outputs = self.linear3(recurrent_features1.contiguous().view(batch_size, -1))
#         return outputs

# # Convenience functions for creating GRU and LSTM models for different datasets

# def create_gru_model(dataset_name, input_dim, seq_len=1, dim_hidden=64):
#     """
#     Factory function to create GRU models for specific datasets.

#     Args:
#         dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
#         input_dim: Number of input features
#         seq_len: Sequence length (window size)
#         dim_hidden: Hidden dimension size

#     Returns:
#         GRU model for the specified dataset
#     """
#     # Dataset-specific configurations
#     dataset_configs = {
#         'carh': {'classes': 5},
#         'cicids2017': {'classes': 8},
#         'toniot': {'classes': 10}
#     }

#     if dataset_name not in dataset_configs:
#         raise ValueError(f"Unsupported dataset: {dataset_name}")

#     config = dataset_configs[dataset_name]
#     num_classes = config['classes']

#     model = GRU(
#         dim_in=input_dim,
#         dim_out=num_classes,
#         lstm_num_layers=1,
#         dim_hidden=dim_hidden
#     )

#     return model

# def create_lstm_model(dataset_name, input_dim, seq_len=1, dim_hidden=128):
#     """
#     Factory function to create LSTM models for specific datasets.

#     Args:
#         dataset_name: Name of the dataset ('carh', 'cicids2017', 'toniot')
#         input_dim: Number of input features
#         seq_len: Sequence length (window size)
#         dim_hidden: Hidden dimension size

#     Returns:
#         LSTM model for the specified dataset
#     """
#     # Dataset-specific configurations
#     dataset_configs = {
#         'carh': {'classes': 5},
#         'cicids2017': {'classes': 8},
#         'toniot': {'classes': 10}
#     }

#     if dataset_name not in dataset_configs:
#         raise ValueError(f"Unsupported dataset: {dataset_name}")

#     config = dataset_configs[dataset_name]
#     num_classes = config['classes']

#     model = LSTM(
#         dim_in=input_dim,
#         dim_out=num_classes,
#         seq=seq_len,
#         dim_hidden=dim_hidden
#     )

#     return model
