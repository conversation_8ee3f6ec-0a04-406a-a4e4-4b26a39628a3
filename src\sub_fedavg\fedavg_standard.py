import copy
import torch
import numpy as np

def FedAvg_standard(w_glob, w_locals, num_samples=None):
    """
    Standard FedAvg aggregation without pruning.
    
    Implements the standard federated averaging algorithm that computes
    the weighted average of client model parameters.
    
    Args:
        w_glob: Global model state dictionary
        w_locals: List of local model state dictionaries from clients
        num_samples: List of number of samples per client (for weighted averaging)
                    If None, uses uniform weighting
    
    Returns:
        Updated global model state dictionary
    """
    if len(w_locals) == 0:
        return w_glob
    
    # Initialize the aggregated weights with the first client's weights
    w_avg = copy.deepcopy(w_locals[0])
    
    # If no sample counts provided, use uniform weighting
    if num_samples is None:
        num_samples = [1.0] * len(w_locals)
    
    # Normalize weights to sum to 1
    total_samples = sum(num_samples)
    weights = [n / total_samples for n in num_samples]
    
    # Aggregate parameters using weighted average
    for key in w_avg.keys():
        # Initialize with zeros
        w_avg[key] = torch.zeros_like(w_avg[key])

        # Handle different parameter types
        if 'num_batches_tracked' in key:
            # For BatchNorm tracking parameters, use simple averaging without weights
            for i, w_local in enumerate(w_locals):
                w_avg[key] += w_local[key]
            w_avg[key] = w_avg[key] // len(w_locals)  # Integer division for tracking counters
        else:
            # Weighted sum for regular parameters (weights, biases)
            for i, w_local in enumerate(w_locals):
                w_avg[key] += w_local[key] * weights[i]
    
    return w_avg

def FedAvg_standard_simple(w_locals):
    """
    Simplified standard FedAvg aggregation with uniform weighting.
    
    Args:
        w_locals: List of local model state dictionaries from clients
    
    Returns:
        Averaged model state dictionary
    """
    if len(w_locals) == 0:
        return {}
    
    # Initialize with the first client's weights
    w_avg = copy.deepcopy(w_locals[0])
    
    # Average all parameters
    for key in w_avg.keys():
        # Initialize with zeros
        w_avg[key] = torch.zeros_like(w_avg[key])
        
        # Sum all client parameters
        for w_local in w_locals:
            w_avg[key] += w_local[key]
        
        # Take average
        w_avg[key] = w_avg[key] / len(w_locals)
    
    return w_avg

def update_global_model(global_model, aggregated_weights):
    """
    Update global model with aggregated weights.
    
    Args:
        global_model: Global model to update
        aggregated_weights: Aggregated weights from FedAvg
    
    Returns:
        Updated global model
    """
    global_model.load_state_dict(aggregated_weights)
    return global_model

def distribute_global_model(global_model, client_models):
    """
    Distribute global model weights to all client models.
    
    Args:
        global_model: Global model with updated weights
        client_models: List of client models to update
    
    Returns:
        List of updated client models
    """
    global_state_dict = global_model.state_dict()
    
    for client_model in client_models:
        client_model.load_state_dict(copy.deepcopy(global_state_dict))
    
    return client_models

def compute_model_difference(model1_state, model2_state):
    """
    Compute the L2 norm difference between two model states.
    
    Args:
        model1_state: First model state dictionary
        model2_state: Second model state dictionary
    
    Returns:
        L2 norm of the difference
    """
    diff_norm = 0.0
    
    for key in model1_state.keys():
        if key in model2_state:
            diff = model1_state[key] - model2_state[key]
            diff_norm += torch.norm(diff).item() ** 2
    
    return np.sqrt(diff_norm)

def get_model_size(model_state):
    """
    Get the total number of parameters in a model.
    
    Args:
        model_state: Model state dictionary
    
    Returns:
        Total number of parameters
    """
    total_params = 0
    
    for key in model_state.keys():
        total_params += model_state[key].numel()
    
    return total_params

def print_model_stats(model_state, model_name="Model"):
    """
    Print statistics about a model.
    
    Args:
        model_state: Model state dictionary
        model_name: Name of the model for printing
    """
    total_params = get_model_size(model_state)
    
    print(f"\n--- {model_name} Statistics ---")
    print(f"Total parameters: {total_params:,}")
    
    for key, param in model_state.items():
        print(f"{key}: {param.shape} ({param.numel():,} params)")
    
    print(f"--- End {model_name} Statistics ---\n")
