import argparse
    
## CIFAR-10 has 50000 training images (5000 per class), 10 classes, 10000 test images (1000 per class)
## CIFAR-100 has 50000 training images (500 per class), 100 classes, 10000 test images (100 per class)
## MNIST has 60000 training images (min: 5421, max: 6742 per class), 10000 test images (min: 892, max: 1135
## per class) --> in the code we fixed 5000 training image per class, and 900 test image per class to be 
## consistent with CIFAR-10 

## CIFAR-10 Non-IID 250 samples per label for 2 class non-iid is the benchmark (500 samples for each client)

def args_parser():
    parser = argparse.ArgumentParser()
    # federated arguments
    parser.add_argument('--rounds', type=int, default=100, help="rounds of training")
    parser.add_argument('--num_users', type=int, default=10, help="number of users: K")
    parser.add_argument('--nclass', type=int, default=2, help="classes or shards per user")
    parser.add_argument('--nsample_pc', type=int, default=250, 
                        help="number of samples per class or shard for each client")
    parser.add_argument('--frac', type=float, default=0.5, help="the fraction of clients: C")
    parser.add_argument('--local_ep', type=int, default=5, help="the number of local epochs: E")
    parser.add_argument('--local_bs', type=int, default=128, help="local batch size: B")
    parser.add_argument('--bs', type=int, default=128, help="test batch size")
    parser.add_argument('--warmup_epoch', type=int, default=0, help="the number of pretrain local ep")

    # model arguments
    parser.add_argument('--model', type=str, default='intrusion_detection',
                        choices=['lenet5', 'intrusion_detection', 'gru', 'lstm', 'transformer_only', 'cnn_only', 'edge_cnn'],
                        help='model name: lenet5 (for image datasets), intrusion_detection (CNN+Transformer for CSV), gru (GRU for sequence data), lstm (LSTM for sequence data), transformer_only (pure Transformer for CSV), cnn_only (pure CNN for CSV), edge_cnn (lightweight CNN for edge computing)')
    parser.add_argument('--ks', type=int, default=5, help='kernel size to use for convolutions')
    parser.add_argument('--in_ch', type=int, default=3, help='input channels of the first conv layer')

    # Adam optimizer arguments
    parser.add_argument('--beta1', type=float, default=0.9, help='Adam beta1 parameter')
    parser.add_argument('--beta2', type=float, default=0.999, help='Adam beta2 parameter')
    parser.add_argument('--eps', type=float, default=0., help='Adam epsilon parameter')#1e-8
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='weight decay (L2 regularization)')
    parser.add_argument('--lr', type=float, default=0.0002, help="learning rate")

    # dataset partitioning arguments
    parser.add_argument('--dataset', type=str, default='cicids2017',
                        choices=['mnist', 'cifar10', 'cifar100', 'carh', 'cicids2017', 'toniot'],
                        help="name of dataset: mnist, cifar10, cifar100, carh, cicids2017, toniot")
    parser.add_argument('--noniid', type=bool, default=True, help='whether i.i.d or not')
    parser.add_argument('--shard', type=bool, default=False, help='whether non-i.i.d based on shard or not')
    parser.add_argument('--label', type=bool, default=False, help='whether non-i.i.d based on label or not')
    parser.add_argument('--dirichlet', type=bool, default=True, help='whether non-i.i.d based on Dirichlet distribution or not')
    parser.add_argument('--alpha', type=float, default=0.5,
                        help='concentration parameter for Dirichlet distribution (lower = more non-IID)')
    parser.add_argument('--window_size', type=int, default=1,
                        help='sequence length for sliding window (CSV datasets): 1=add dim T=1, >1=sliding window with step=1, output shape (N, T, f)')
    parser.add_argument('--split_test', type=bool, default=True,
                        help='whether split test set in partitioning or not')

    # intrusion detection model arguments (CNN + Transformer fusion)
    parser.add_argument('--cnn_layers', type=int, default=2,
                        help='number of CNN layers in the intrusion detection model')
    parser.add_argument('--transformer_layers', type=int, default=2,
                        help='number of Transformer layers in the intrusion detection model')
    parser.add_argument('--d_model', type=int, default=128,
                        help='Transformer model dimension')
    parser.add_argument('--nhead', type=int, default=4,
                        help='number of attention heads in Transformer')
    parser.add_argument('--hidden_dim', type=int, default=128,
                        help='hidden dimension for CNN channels')
    parser.add_argument('--model_dropout', type=float, default=0.2,
                        help='dropout rate for intrusion detection model')
    parser.add_argument('--cnn_channels', type=int, default=64,
                        help='number of CNN channels for IntrusionDetectionModel CNN path')

    # transformer_only model specific arguments
    parser.add_argument('--transformer_only_layers', type=int, default=2,
                        help='number of Transformer layers for transformer_only model (default: 4)')
    parser.add_argument('--transformer_only_d_model', type=int, default=128,
                        help='Transformer model dimension for transformer_only model (default: 256)')
    parser.add_argument('--transformer_only_nhead', type=int, default=4,
                        help='number of attention heads for transformer_only model (default: 8)')
    parser.add_argument('--transformer_only_hidden_dim', type=int, default=128,
                        help='hidden dimension for classifier in transformer_only model (default: 128)')

    # cnn_only model specific arguments
    parser.add_argument('--cnn_only_layers', type=int, default=3,
                        help='number of CNN layers for cnn_only model (default: 3)')
    parser.add_argument('--cnn_only_hidden_dim', type=int, default=256,
                        help='hidden dimension for classifier in cnn_only model (default: 512)')

    # edge_cnn model specific arguments
    parser.add_argument('--edge_cnn_hidden_dim', type=int, default=64,
                        help='hidden dimension for EdgeCNN model (optimized for edge computing, default: 64)')

    # light intrusion_detection model specific arguments
    parser.add_argument('--light_d_model', type=int, default=32,
                        help='model dimension for light CNN + Transformer fusion model (default: 32)')
    parser.add_argument('--light_nhead', type=int, default=1,
                        help='number of attention heads for cross-attention in light model (default: 1)')
    parser.add_argument('--light_hidden_dim', type=int, default=64,
                        help='hidden dimension for classifier in light model (default: 64)')

    # pruning arguments
    parser.add_argument('--disable_pruning', type=bool, default=True,
                        help="Disable model pruning (use standard FedAvg)")
    parser.add_argument('--pruning_percent', type=float, default=10,
                        help="Pruning percent for layers (0-100)")
    parser.add_argument('--pruning_target', type=int, default=30, 
                        help="Total Pruning target percentage (0-100)")
    parser.add_argument('--dist_thresh', type=float, default=0.0001, 
                        help="threshold for fcs masks difference ")
    parser.add_argument('--acc_thresh', type=int, default=50,
                        help="accuracy threshold to apply the derived pruning mask")
    # other arguments
    parser.add_argument('--gpu', type=int, default=0, help="GPU ID, -1 for CPU")
    parser.add_argument('--is_print', type=bool, default=True, help='verbose print')
    parser.add_argument('--print_freq', type=int, default=10, help="printing frequency during training rounds")
    parser.add_argument('--seed', type=int, default=1, help='random seed (default: 1)')
    parser.add_argument('--load_initial', type=str, default='', help='define initial model path')
    parser.add_argument('--results_save', type=str, default='/', help='define fed results save folder')
    parser.add_argument('--start_saving', type=int, default=0, help='when to start saving models')

    args = parser.parse_args()
    return args
